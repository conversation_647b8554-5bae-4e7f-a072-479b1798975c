# Additional Software Installation - **MOSTLY OPTIONAL**
## Essential Applications and Development Tools

This guide covers installing additional software after completing your desktop setup.

**Prerequisites**: 
- Complete `01-arch-base-installation.md` 
- Complete `02-post-install-hardware-setup.md`
- Complete desktop setup (QuickShell, Waybar, or Hybrid)

---

## 1. Enable Multilib (32-bit support)

### Enable Multilib Repository
```bash
sudo nano /etc/pacman.conf
```

Uncomment these lines:
```ini
[multilib]
Include = /etc/pacman.d/mirrorlist
```

Then update:
```bash
sudo pacman -Syy                             # Refresh with multilib
```

---

## 2. Chaotic AUR Integration

### Chaotic AUR is Already Configured
**Chaotic AUR was set up in step 3, so you can now install AUR packages directly with pacman!**

```bash
# Example: Install AUR packages directly (no need for paru/yay)
sudo pacman -S quickshell-git                # Install QuickShell directly from Chaotic AUR
```

---

## 3. Development Tools - **OPTIONAL**

### Programming Languages and Tools
```bash
sudo pacman -S code docker docker-compose nodejs npm python python-pip rust cargo go base-devel cmake clang  # Development tools and languages
```

### Version Control and Collaboration
```bash
sudo pacman -S git github-cli gitlab-cli mercurial subversion  # Version control systems and tools
```

### Database Tools
```bash
sudo pacman -S postgresql mysql sqlite redis mongodb-bin  # Database systems and tools
```

### Container and Virtualization
```bash
sudo pacman -S docker docker-compose podman qemu-full virt-manager virtualbox  # Container and virtualization tools

# Enable Docker service
sudo systemctl enable docker
sudo usermod -aG docker $USERNAME
```

---

## 4. Essential Applications

### Web Browsers
```bash
# Basic applications (install as needed)
sudo pacman -S firefox                       # Web browser
sudo pacman -S chromium                      # Chromium browser
paru -S google-chrome                        # Google Chrome (AUR)
paru -S brave-bin                            # Brave browser (AUR)
```

### Media and Graphics
```bash
sudo pacman -S vlc                          # Media player
sudo pacman -S mpv                          # Lightweight media player
sudo pacman -S gimp                         # Image editing
sudo pacman -S inkscape                     # Vector graphics
sudo pacman -S blender                      # 3D modeling
sudo pacman -S obs-studio                   # Screen recording/streaming
sudo pacman -S kdenlive                     # Video editing
```

### Office and Productivity - **OPTIONAL**
```bash
sudo pacman -S libreoffice-fresh            # Office suite
sudo pacman -S thunderbird                  # Email client
sudo pacman -S calibre                      # E-book management
sudo pacman -S zathura                      # PDF viewer
sudo pacman -S zathura-pdf-mupdf            # PDF support for zathura
```

### Communication
```bash
sudo pacman -S discord                      # Discord chat
sudo pacman -S telegram-desktop             # Telegram
paru -S slack-desktop                       # Slack (AUR)
paru -S zoom                                # Zoom (AUR)
paru -S teams                               # Microsoft Teams (AUR)
```

---

## 5. Gaming Setup - **OPTIONAL**

### Steam and Gaming Platforms
```bash
sudo pacman -S steam                        # Steam gaming platform
sudo pacman -S lutris                       # Game launcher
sudo pacman -S wine                         # Windows compatibility
sudo pacman -S winetricks                   # Wine configuration
paru -S heroic-games-launcher               # Epic Games launcher
```

### Gaming Libraries and Tools
```bash
sudo pacman -S lib32-mesa                   # 32-bit graphics drivers
sudo pacman -S lib32-nvidia-utils           # 32-bit NVIDIA drivers
sudo pacman -S gamemode                     # Gaming optimizations
sudo pacman -S mangohud                     # Gaming overlay
sudo pacman -S goverlay                     # MangoHUD configurator
```

### Game Development
```bash
sudo pacman -S godot                        # Godot game engine
sudo pacman -S unity-editor                 # Unity (if available)
paru -S unreal-engine                       # Unreal Engine (AUR)
```

---

## 6. System Utilities

### File Management
```bash
sudo pacman -S ranger                       # Terminal file manager
sudo pacman -S nemo                         # File manager
sudo pacman -S ark                          # Archive manager
sudo pacman -S rsync                        # File synchronization
sudo pacman -S rclone                       # Cloud storage sync
```

### System Monitoring
```bash
sudo pacman -S btop \                        # Modern resource monitor
               iotop \                       # I/O monitoring  
               nethogs \                     # Network usage per process
               nvtop \                       # NVIDIA GPU monitoring
               smartmontools \               # Hard drive health
               lm_sensors                    # Hardware sensors
```

### Network Tools
```bash
sudo pacman -S wireshark-qt                 # Network analysis
sudo pacman -S nmap                         # Network scanning
sudo pacman -S wget                         # File downloading
sudo pacman -S curl                         # HTTP client
sudo pacman -S openssh                      # SSH client/server
```

---

## 7. Security and Privacy

### Security Tools
```bash
sudo pacman -S ufw                           # Uncomplicated Firewall
sudo pacman -S clamav                        # Antivirus
sudo pacman -S rkhunter                      # Rootkit hunter
sudo pacman -S lynis                         # Security auditing
```

### Password Management
```bash
sudo pacman -S keepassxc                     # Password manager
paru -S bitwarden                           # Bitwarden (AUR)
sudo pacman -S pass                         # Command-line password manager
```

### VPN and Privacy
```bash
sudo pacman -S openvpn                      # OpenVPN client
sudo pacman -S wireguard-tools              # WireGuard VPN
paru -S nordvpn-bin                         # NordVPN (AUR)
paru -S protonvpn                           # ProtonVPN (AUR)
```

---

## 8. Multimedia Production

### Audio Production - **OPTIONAL**
```bash
sudo pacman -S audacity                     # Audio editing
sudo pacman -S ardour                       # Digital Audio Workstation
sudo pacman -S lmms                         # Music production
sudo pacman -S jack2                        # Professional audio
```

### Video Production
```bash
sudo pacman -S davinci-resolve              # Professional video editing
sudo pacman -S shotcut                      # Simple video editor
sudo pacman -S handbrake                    # Video transcoding
sudo pacman -S ffmpeg                       # Video processing
```

### Graphics and Design
```bash
sudo pacman -S krita                        # Digital painting
sudo pacman -S darktable                    # Photo processing
sudo pacman -S rawtherapee                  # RAW photo editor
sudo pacman -S scribus                      # Desktop publishing
```

---

## 9. Scientific and Educational

### Mathematics and Science
```bash
sudo pacman -S octave                       # MATLAB alternative
sudo pacman -S scilab                       # Scientific computing
sudo pacman -S maxima                       # Computer algebra system
sudo pacman -S geogebra                     # Mathematics software
```

### Research and Writing
```bash
sudo pacman -S texlive-most                 # LaTeX distribution
sudo pacman -S texmaker                     # LaTeX editor
sudo pacman -S zotero                       # Reference manager
sudo pacman -S jabref                       # Bibliography manager
```

---

## 10. Cloud and Remote Access

### Cloud Storage
```bash
sudo pacman -S nextcloud-client             # Nextcloud sync
paru -S dropbox                             # Dropbox (AUR)
paru -S google-drive-ocamlfuse              # Google Drive (AUR)
sudo pacman -S syncthing                    # P2P file sync
```

### Remote Access
```bash
sudo pacman -S remmina                      # Remote desktop client
sudo pacman -S freerdp                      # RDP protocol support
sudo pacman -S tigervnc                     # VNC client/server
paru -S teamviewer                          # TeamViewer (AUR)
```

---

## 11. Fonts and Themes

### Additional Fonts
```bash
# Programming fonts
sudo pacman -S ttf-jetbrains-mono           # JetBrains Mono
sudo pacman -S ttf-fira-code                # Fira Code with ligatures
sudo pacman -S ttf-cascadia-code            # Cascadia Code

# System fonts
sudo pacman -S noto-fonts-cjk               # Asian language support
sudo pacman -S ttf-liberation               # Liberation fonts
sudo pacman -S adobe-source-fonts           # Adobe Source fonts
```

### Icon Themes and Cursors
```bash
sudo pacman -S papirus-icon-theme           # Papirus icons
sudo pacman -S arc-icon-theme               # Arc icons
sudo pacman -S breeze-icons                 # Breeze icons
sudo pacman -S xcursor-themes               # Additional cursor themes
```

---

## 12. Backup and Synchronization

### Backup Solutions
```bash
sudo pacman -S rsync                        # File synchronization
sudo pacman -S borgbackup                   # Deduplicating backup
sudo pacman -S timeshift                    # System snapshots
paru -S urbackup-client                     # UrBackup client (if not already installed)
```

### File Synchronization
```bash
sudo pacman -S syncthing                    # Peer-to-peer sync
sudo pacman -S unison                       # Two-way file sync
sudo pacman -S lsyncd                       # Live sync daemon
```

---

## 13. Performance and Optimization - **OPTIONAL**

### System Optimization
```bash
sudo pacman -S preload                      # Application preloader
sudo pacman -S irqbalance                   # IRQ balancing
sudo pacman -S thermald                     # Thermal management
sudo pacman -S tlp                          # Power management (laptops)
```

### Gaming Optimization
```bash
sudo pacman -S gamemode                     # Gaming performance mode
sudo pacman -S lib32-gamemode               # 32-bit gamemode support
paru -S auto-cpufreq                        # Automatic CPU frequency scaling
```

---

## 14. Specialized Tools

### 3D Printing
```bash
sudo pacman -S cura                         # 3D printing slicer
sudo pacman -S prusa-slicer                 # PrusaSlicer
sudo pacman -S openscad                     # 3D CAD modeling
```

### Electronics and Engineering
```bash
sudo pacman -S kicad                        # PCB design
sudo pacman -S freecad                      # 3D CAD
sudo pacman -S arduino-ide                  # Arduino development
paru -S platformio                          # IoT development platform
```

### Cryptocurrency
```bash
paru -S electrum                            # Bitcoin wallet
paru -S monero-gui                          # Monero wallet
sudo pacman -S ccminer                      # Cryptocurrency mining
```

---

## 15. Configuration and Management

### Dotfiles Management
```bash
sudo pacman -S stow                         # Symlink manager
paru -S chezmoi                             # Dotfiles manager
sudo pacman -S git                          # For dotfiles repositories
```

### System Configuration
```bash
sudo pacman -S dconf-editor                 # GNOME settings editor
sudo pacman -S qt5ct                        # Qt5 configuration
sudo pacman -S lxappearance                 # GTK theme configuration
```

---

## 16. Enable and Configure Services

### Enable Docker
```bash
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USERNAME
```

### Enable Bluetooth (if not already done)
```bash
sudo systemctl enable bluetooth
sudo systemctl start bluetooth
```

### Enable Printing Support
```bash
sudo pacman -S cups cups-pdf               # Printing system
sudo systemctl enable cups
sudo systemctl start cups
```

### Enable Network Time Synchronization
```bash
sudo systemctl enable systemd-timesyncd
sudo systemctl start systemd-timesyncd
```

---

## 17. Post-Installation Configuration

### Configure Git
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main
```

### Configure SSH
```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_ed25519
```

### Configure Firewall
```bash
sudo ufw enable                              # Enable firewall
sudo ufw default deny incoming              # Block incoming by default
sudo ufw default allow outgoing             # Allow outgoing
sudo ufw logging on                         # Enable logging

# Allow SSH (if needed)
sudo ufw allow ssh                          # Allow SSH connections
```

---

## 18. Application-Specific Configurations

### Configure VS Code
```bash
# Install useful extensions
code --install-extension ms-python.python
code --install-extension rust-lang.rust-analyzer
code --install-extension ms-vscode.cpptools
code --install-extension bradlc.vscode-tailwindcss
```

### Configure Steam
```bash
# Enable Proton for Windows games
# This is done through Steam settings:
# Steam > Settings > Steam Play > Enable Steam Play for all other titles
```

### Configure OBS Studio
```bash
# Install additional plugins
paru -S obs-studio-plugin-browser
paru -S obs-studio-plugin-websocket
```

---

## ✅ Additional Software Installation Complete!

Your system now includes:
- ✅ Development tools and environments
- ✅ Essential applications
- ✅ Gaming setup with optimization
- ✅ Multimedia production tools
- ✅ Security and privacy tools
- ✅ Cloud and remote access
- ✅ Backup and synchronization
- ✅ Performance optimization

## Next Steps

Continue with:
1. **Maintenance Guide**: `10-maintenance-and-troubleshooting.md` - System maintenance and troubleshooting

## Usage Tips

- **Use paru for AUR packages**: `paru -S package-name`
- **Update all packages**: `paru -Syu`
- **Search packages**: `paru -Ss search-term`
- **Clean package cache**: `sudo pacman -Sc`
- **Remove orphaned packages**: `sudo pacman -Qtdq | sudo pacman -Rns -`

## Customization Ideas

- Create application launchers for frequently used programs
- Set up custom keyboard shortcuts for applications
- Configure application-specific desktop environments
- Create backup scripts for important application data
- Set up automated updates for critical applications

---

*Additional software guide for Arch Linux 2025 | Complete Application Suite*