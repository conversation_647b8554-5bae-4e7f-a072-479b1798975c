# Arch Linux Base Installation Guide - Btrfs Edition
## UK Location | Intel CPU | RTX 3090 | Clean Base System

This is a **step-by-step, commented, bulletproof installation guide** for a clean Arch Linux base system with:

**📋 OPTIONAL SECTIONS GUIDE**: Sections marked with **OPTIONAL** can be skipped without affecting core functionality. The system will work perfectly without them.
- **UK location** (locale, keymap, mirrors, timezone)
- **Intel CPU + RTX 3090** (kernel, firmware, basic drivers)
- **Btrfs filesystem** (modern, CoW, snapshots, compression)
- **Clean base system** (no desktop environment - see other guides for desktop setup)
- **Future-proof design** (ready for NVMe migration and modular desktop additions)

---

## 0. Pre-boot Checklist

1. **Download the latest Arch ISO** from https://archlinux.org/download/
2. **Create bootable USB** using Rufus (Windows) or `dd` (Linux)
3. **Boot in UEFI mode** (not legacy BIOS)
4. **Verify UEFI boot**:
   ```bash
   ls /sys/firmware/efi/efivars    # Should list files, not give "No such file"
   ```
   *If this fails, you booted in BIOS mode - restart and select UEFI boot option*

---

## 1. Set Console Keymap & Time

```bash
loadkeys uk                                   # Set UK keyboard layout
timedatectl set-ntp true                      # Enable network time sync
timedatectl set-timezone Europe/London        # Set UK timezone
timedatectl status                            # Verify time settings
```

**Explanation**: The UK keymap ensures @ and " symbols work correctly. NTP sync ensures accurate time for package verification and logs.

---

## 2. Networking (Optional SSH for Comfort) - **OPTIONAL**

**⚠️ OPTIONAL**: Skip this entire section if you're installing directly on the machine (not remotely).

```bash
systemctl start sshd                          # Start SSH daemon
passwd                                        # Set temporary root password for SSH
ip addr                                       # Note your IP address
```

**Explanation**: SSH allows you to copy-paste commands from another computer, reducing typos. The password is temporary - we'll disable root login later.

---

## 3. Mirror List - Verified UK Mirrors

```bash
nano /etc/pacman.d/mirrorlist                 # Edit mirror list
```

**Delete everything** and paste these **verified UK mirrors**:

```text
## UK mirrors - verified fast and reliable 2025
Server = https://mirror.bytemark.co.uk/archlinux/$repo/os/$arch
Server = https://archlinux.uk.mirror.allworldit.com/archlinux/$repo/os/$arch
Server = https://gb.mirrors.cicku.me/archlinux/$repo/os/$arch
Server = https://london.mirror.pkgbuild.com/$repo/os/$arch
Server = https://mirrors.gethosted.online/archlinux/$repo/os/$arch
Server = https://www.mirrorservice.org/sites/ftp.archlinux.org/$repo/os/$arch
Server = https://mirror.netweaver.uk/archlinux/$repo/os/$arch
Server = https://mirrors.ukfast.co.uk/sites/archlinux.org/$repo/os/$arch
```

Save with `Ctrl+O`, `Enter`, then `Ctrl+X`. Refresh package database:

```bash
pacman -Syy                                   # Force refresh package database
```

**Explanation**: Using UK-only mirrors ensures fastest download speeds. Multiple mirrors provide redundancy if one goes down.

---

## 4. Btrfs Disk Layout (Modern & Future-Proof Design)

### Why Btrfs?

**Btrfs advantages**:
- **Copy-on-Write (CoW)**: Prevents data corruption, enables snapshots
- **Built-in compression**: Saves 20-40% disk space automatically
- **Subvolumes**: Flexible partitioning without fixed sizes
- **Snapshots**: Instant system backups before updates
- **Self-healing**: Detects and fixes bit rot with RAID
- **Online resizing**: Grow/shrink filesystems while mounted
- **Transparent compression**: lzo, zstd compression built-in

| Partition | Size | Mount | File System | Purpose |
|-----------|------|-------|-------------|---------|
| /dev/nvme1n1p1 | 1 GiB | /boot | FAT32 | EFI System Partition (ESP) |
| /dev/nvme1n1p2 | Remaining | / | Btrfs | Root with subvolumes |

### Btrfs Subvolume Layout

```
/dev/sda2 (Btrfs root volume)
├── @ (mounted at /)                         # Root subvolume
├── @home (mounted at /home)                 # Home subvolume
├── @var (mounted at /var)                   # Variable data subvolume
├── @tmp (mounted at /tmp)                   # Temporary files subvolume
├── @snapshots (mounted at /.snapshots)     # Snapshots storage
└── @swap (swapfile location)                # Swap subvolume
```

**Explanation**: This layout allows independent snapshots of root, home, and var. System updates can be rolled back without affecting user data.

### Identify Your Storage Devices (Critical Step)

**First, list all storage devices to identify your target drive**:
```bash
lsblk                                        # List all block devices
fdisk -l                                     # List all disks with partition tables
ls /dev/nvme* /dev/sd* 2>/dev/null           # Show available NVMe and SATA drives
```

**Get detailed information about each drive**:
```bash
lsblk -o NAME,SIZE,TYPE,MOUNTPOINT,MODEL,SERIAL  # Detailed block device info
```

**For NVMe drives, get more details**:
```bash
nvme list                                    # List NVMe drives with details (if available)
```

**Example output interpretation**:
```
NAME        SIZE TYPE MOUNTPOINT MODEL
nvme1n1     1TB  disk            Samsung SSD 980 PRO 1TB
├─nvme1n1p1 1G   part /boot/efi  
└─nvme1n1p2 999G part /          
nvme2n1     2TB  disk            WD Black SN850 2TB
sda         500G disk            External USB Drive
```

**⚠️ CRITICAL: Identify your target drive before proceeding!**
- **NVMe drives**: Usually `/dev/nvme0n1`, `/dev/nvme1n1`, etc.
- **SATA drives**: Usually `/dev/sda`, `/dev/sdb`, etc.
- **Check the SIZE and MODEL** to confirm the correct drive

**Replace `/dev/sda` with your actual drive in all following commands!**
For this guide, we'll use `/dev/nvme1n1` as an example (adjust as needed).

### Create Partitions

```bash
gdisk /dev/nvme1n1                           # Use GPT (not MBR) for UEFI - REPLACE with your drive!
```

**⚠️ DOUBLE-CHECK: Make sure you're using the correct drive path!**

**In gdisk, type these commands exactly**:
```
p                                             # Print current partition table (verify correct drive)
o                                             # Create new empty GPT partition table
y                                             # Confirm deletion of existing data
n                                             # Create new partition 1 (EFI)
1                                             # Partition number 1
[Enter]                                       # Default first sector (2048)
+1G                                           # Size: 1 GiB
ef00                                          # Type: EFI System
n                                             # Create new partition 2 (Btrfs root)
2                                             # Partition number 2
[Enter]                                       # Default first sector
[Enter]                                       # Use remaining space
8300                                          # Type: Linux filesystem
p                                             # Print partition table to verify
w                                             # Write changes and exit
y                                             # Confirm changes
```

### Format Partitions

```bash
mkfs.fat -F 32 -n "EFI" /dev/nvme1n1p1       # Format EFI partition as FAT32 with label
mkfs.btrfs -L "arch-root" /dev/nvme1n1p2     # Format root as Btrfs with label
```

**Explanation**: `-L` sets filesystem labels for easy identification. Btrfs creates a single volume that we'll subdivide with subvolumes.

### Create Btrfs Subvolumes

```bash
# Mount the Btrfs root volume temporarily
mount /dev/nvme1n1p2 /mnt                    # Mount your Btrfs partition

# Create subvolumes
btrfs subvolume create /mnt/@                 # Root subvolume
btrfs subvolume create /mnt/@home             # Home subvolume  
btrfs subvolume create /mnt/@var              # Var subvolume
btrfs subvolume create /mnt/@tmp              # Tmp subvolume
btrfs subvolume create /mnt/@snapshots        # Snapshots subvolume
btrfs subvolume create /mnt/@swap             # Swap subvolume (no CoW)

# Disable CoW for swap subvolume (required for swapfiles)
chattr +C /mnt/@swap                          # Disable copy-on-write for swap

# List created subvolumes
btrfs subvolume list /mnt                     # Verify subvolumes were created

# Unmount to remount with proper subvolumes
umount /mnt                                   # Unmount to prepare for proper mounting
```

**Explanation**: Subvolumes act like separate filesystems but share the same Btrfs volume. CoW is disabled for swap to prevent performance issues.

### Mount Subvolumes with Optimizations

```bash
# Define Btrfs mount options for performance and compression
BTRFS_OPTS="defaults,noatime,compress=zstd:3,space_cache=v2,discard=async"

# Mount root subvolume
mount -o $BTRFS_OPTS,subvol=@ /dev/nvme1n1p2 /mnt  # Mount root subvolume

# Create mount directories
mkdir -p /mnt/{home,var,tmp,.snapshots,swap,boot}  # Create mount points

# Mount other subvolumes
mount -o $BTRFS_OPTS,subvol=@home /dev/nvme1n1p2 /mnt/home      # Mount home subvolume
mount -o $BTRFS_OPTS,subvol=@var /dev/nvme1n1p2 /mnt/var        # Mount var subvolume
mount -o $BTRFS_OPTS,subvol=@tmp /dev/nvme1n1p2 /mnt/tmp        # Mount tmp subvolume
mount -o $BTRFS_OPTS,subvol=@snapshots /dev/nvme1n1p2 /mnt/.snapshots  # Mount snapshots subvolume
mount -o defaults,noatime,subvol=@swap /dev/nvme1n1p2 /mnt/swap  # Mount swap subvolume

# Mount EFI partition
mkdir -p /mnt/boot/EFI                        # Create EFI mount point
mount /dev/nvme1n1p1 /mnt/boot/EFI           # Mount EFI partition
```

**Mount Options Explained**:
- `noatime`: Don't update access times (performance boost)
- `compress=zstd:3`: Enable zstd compression level 3 (good balance)
- `space_cache=v2`: Faster free space tracking
- `discard=async`: Async TRIM for SSDs (better performance)
- `subvol=@`: Mount specific subvolume

---

## 5. Install Base System + Essential Tools

```bash
pacstrap -K /mnt base linux linux-firmware linux-headers linux-lts linux-lts-headers intel-ucode base-devel sudo nano vim git networkmanager grub efibootmgr dosfstools mtools openssh reflector neovim fastfetch htop wget curl man-db man-pages btrfs-progs snapper snap-pac grub-btrfs
```

**Btrfs-specific Packages**:
- `btrfs-progs`: Btrfs utilities and management tools
- `snapper`: Automated snapshot management
- `snap-pac`: Automatic snapshots before/after pacman operations
- `grub-btrfs`: Boot from snapshots in GRUB menu

**Other Package Explanations**:
- `base`: Core Arch Linux system
- `linux` + `linux-lts`: Current and Long Term Support kernels
- `linux-headers` + `linux-lts-headers`: Needed for NVIDIA drivers
- `intel-ucode`: CPU microcode updates for Intel processors
- `networkmanager`: Easy network configuration
- `grub` + `efibootmgr`: UEFI bootloader
- `dosfstools` + `mtools`: Tools for FAT32 EFI partition
- `reflector`: Automatically updates mirror list

---

## 6. Generate fstab & Enter chroot

```bash
genfstab -U /mnt >> /mnt/etc/fstab            # Generate filesystem table using UUIDs
cat /mnt/etc/fstab                           # Verify fstab looks correct
arch-chroot /mnt                             # Enter the new system
```

**Explanation**: `fstab` tells the system which partitions to mount at boot. UUIDs are more reliable than device names which can change.

---

## 7. Configure the New System

### Locale (Language & Region)
```bash
echo "en_GB.UTF-8 UTF-8" > /etc/locale.gen   # Enable UK English locale
locale-gen                                   # Generate locale files
echo "LANG=en_GB.UTF-8" > /etc/locale.conf   # Set system language
```

### Console Keymap & Font
```bash
echo "KEYMAP=uk" > /etc/vconsole.conf        # Set console keyboard to UK layout
```

### Hostname Configuration
```bash
hostnamectl set-hostname archlinux-workstation
cat > /etc/hosts << "EOF"
127.0.0.1    localhost
::1          localhost
*********    archlinux-workstation.localdomain archlinux-workstation
EOF
```

### Time Zone Configuration
```bash
ln -sf /usr/share/zoneinfo/Europe/London /etc/localtime  # Set UK timezone
hwclock --systohc --utc                      # Set hardware clock to UTC
```

---

## 8. Boot Loader Configuration

```bash
grub-install --target=x86_64-efi --bootloader-id=grub --efi-directory=/boot/EFI --removable  # Install GRUB for UEFI x86_64
```

```bash
grub-mkconfig -o /boot/grub/grub.cfg         # Generate GRUB configuration
```

**Explanation**: GRUB will automatically detect Btrfs subvolumes. With grub-btrfs installed, snapshots will appear in the boot menu for system recovery.

---

## 9. Swap File on Btrfs

```bash
# Navigate to swap subvolume
cd /swap

# Create 4GB swap file (adjust size as needed)
truncate -s 0 ./swapfile                     # Create empty file
chattr +C ./swapfile                         # Disable CoW for swap file
btrfs property set ./swapfile compression none  # Disable compression
fallocate -l 4G ./swapfile                   # Allocate 4GB
chmod 600 ./swapfile                         # Set secure permissions
mkswap ./swapfile                            # Initialize swap file
swapon ./swapfile                            # Test swap file

# Add to fstab
echo '/swap/swapfile none swap defaults 0 0' >> /etc/fstab
```

**Btrfs Swap Explanation**: 
- Swap files on Btrfs require CoW and compression disabled
- Must use `truncate` + `fallocate` instead of `dd`
- Placed in separate subvolume to exclude from snapshots

---

## 10. User Account Setup
```bash
# One-time: set your desired username
read -p "Enter your desired username: " USERNAME
```

## 11. User Account & Sudo Configuration

```bash
# Create user with home directory and essential groups
useradd -m -G wheel,video,audio,input,storage,optical,scanner -s /bin/bash "$USERNAME"
# Set user password (choose strong password)
passwd "$USERNAME"                         
```

```bash
# Edit sudo configuration safely
EDITOR=nano visudo                           
```

**In visudo, find and uncomment this line**:
```
%wheel ALL=(ALL:ALL) ALL
```

---

## 12. Enable Chaotic AUR Repository

```bash
# Import Chaotic AUR key
pacman-key --recv-key 3056513887B78AEB --keyserver keyserver.ubuntu.com  # Import Chaotic AUR key
pacman-key --lsign-key 3056513887B78AEB  # Locally sign the key

# Install Chaotic AUR packages
pacman -U 'https://cdn-mirror.chaotic.cx/chaotic-aur/chaotic-keyring.pkg.tar.zst'  # Install keyring
pacman -U 'https://cdn-mirror.chaotic.cx/chaotic-aur/chaotic-mirrorlist.pkg.tar.zst'  # Install mirrorlist

# Add Chaotic AUR to pacman configuration
echo -e '\n[chaotic-aur]\nInclude = /etc/pacman.d/chaotic-mirrorlist' >> /etc/pacman.conf  # Add repository
pacman -Syy                                   # Refresh package databases
```

**Explanation**: Chaotic AUR provides pre-compiled AUR packages, eliminating the need for manual compilation. This saves significant time during installation.

---

## 12. Configure Snapper for Automatic Snapshots

```bash
# Create snapper config for root subvolume
snapper -c root create-config /

# Configure snapper settings
sed -i 's/TIMELINE_LIMIT_HOURLY="10"/TIMELINE_LIMIT_HOURLY="5"/' /etc/snapper/configs/root
sed -i 's/TIMELINE_LIMIT_DAILY="10"/TIMELINE_LIMIT_DAILY="7"/' /etc/snapper/configs/root
sed -i 's/TIMELINE_LIMIT_WEEKLY="0"/TIMELINE_LIMIT_WEEKLY="4"/' /etc/snapper/configs/root
sed -i 's/TIMELINE_LIMIT_MONTHLY="10"/TIMELINE_LIMIT_MONTHLY="12"/' /etc/snapper/configs/root
sed -i 's/TIMELINE_LIMIT_YEARLY="10"/TIMELINE_LIMIT_YEARLY="3"/' /etc/snapper/configs/root

# Enable snapper services
systemctl enable snapper-timeline.timer
systemctl enable snapper-cleanup.timer

# Create initial snapshot
snapper -c root create --description "Fresh Arch installation"
```

**Explanation**: Snapper will automatically create snapshots before package updates and on a timeline. These settings keep a reasonable number of snapshots without filling your disk.

---

## 13. Enable Essential Services & First Reboot

```bash
# Enable NetworkManager for internet connectivity
systemctl enable NetworkManager

# Enable SSH for remote access (optional)
systemctl enable sshd

# Enable reflector for automatic mirror updates
systemctl enable reflector.timer

# Enable SSD TRIM for better performance
systemctl enable fstrim.timer

# Set root password
passwd                                       # Set root password (for emergency access)
```

```bash
exit                                         # Leave chroot environment
umount -R /mnt                               # Unmount all partitions
reboot                                       # Restart to test installation
```

**Remove USB drive when system shuts down**. If everything worked, you'll see GRUB menu then login prompt.

---

## ✅ Base Installation Complete!

You now have a clean, functional Arch Linux base system with:
- ✅ Btrfs filesystem with optimized subvolumes
- ✅ Automatic snapshots configured
- ✅ UK locale and timezone
- ✅ User account with sudo access
- ✅ Essential services enabled
- ✅ Ready for hardware-specific setup

## Next Steps

After successful reboot and login, continue with:
1. **Hardware Setup**: `02-post-install-hardware-setup.md` - Intel CPU and NVIDIA RTX 3090 configuration
2. **Backup Solution**: `03-backup-with-urbackup.md` - Create full system backup
3. **Display Manager**: `04-display-manager-greetd.md` - Set up login manager
4. **Desktop Environment**: Choose from Hyprland, QuickShell, or other desktop guides

---

*Base installation guide for Arch Linux 2025 | Intel + NVIDIA + Btrfs | UK Configuration*