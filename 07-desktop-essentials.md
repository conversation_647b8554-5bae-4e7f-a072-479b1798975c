# Desktop Essentials Setup
## Common Wayland Desktop Tools

This guide installs essential desktop tools that are needed regardless of which desktop shell you choose (QuickShell, Waybar, or Hybrid).

**Prerequisites**:
- Complete `01-arch-base-installation.md`
- Complete `02-post-install-hardware-setup.md`
- Complete `04-display-manager-greetd.md`
- Complete `05-wayland-essentials.md`
- Complete `06-hyprland-setup.md`

**Important**: Set your username variable first:
```bash
# Re-use the username you created
USERNAME="$(logname 2>/dev/null || whoami)"
```

---

## 1. Why Desktop Essentials?

These tools provide core desktop functionality that every desktop environment needs:
- **Application launcher**: Launch applications quickly
- **Screen locker**: Secure your desktop when away
- **Wallpaper manager**: Set desktop backgrounds
- **Notification system**: Display system and app notifications
- **Idle management**: Handle screen timeout and power management
- **Network management**: GUI for network connections

---

## 2. Install Desktop Essential Tools

### Install Common Desktop Tools
```bash
sudo pacman -S wofi swaylock-effects swayidle swaybg mako network-manager-applet  # Common desktop tools
```

**Package explanations**:
- `wofi`: Modern application launcher for Wayland
- `swaylock-effects`: Screen locker with blur and visual effects
- `swayidle`: Manages idle timeouts and screen locking
- `swaybg`: Sets wallpapers on Wayland
- `mako`: Lightweight notification daemon
- `network-manager-applet`: GUI for managing network connections

---

## 3. Configure Desktop Essentials

### Configure Wofi (Application Launcher)
```bash
mkdir -p ~/.config/wofi

cat > ~/.config/wofi/config << 'EOF'
width=600
height=400
location=center
show=drun
prompt=Search...
filter_rate=100
allow_markup=true
no_actions=true
halign=fill
orientation=vertical
content_halign=fill
insensitive=true
allow_images=true
image_size=40
gtk_dark=true
EOF
```

### Configure Mako (Notifications)
```bash
mkdir -p ~/.config/mako

cat > ~/.config/mako/config << 'EOF'
# Mako notification daemon configuration

# Appearance
background-color=#1a1a1a
text-color=#ffffff
border-color=#33ccff
border-size=2
border-radius=10
padding=15
margin=10

# Behavior
default-timeout=5000
ignore-timeout=1
max-visible=5
sort=-time

# Position
anchor=top-right
width=350
height=100

# Font
font=Inter 11

# Icons
icons=1
max-icon-size=48
icon-path=/usr/share/icons/Papirus-Dark

# Actions
actions=1
EOF
```

### Configure Swaylock (Screen Locker)
```bash
mkdir -p ~/.config/swaylock

cat > ~/.config/swaylock/config << 'EOF'
# Swaylock configuration

# Appearance
color=1a1a1a
inside-color=1a1a1a
line-color=33ccff
ring-color=33ccff
text-color=ffffff

# Effects
effect-blur=7x5
effect-vignette=0.5:0.5
fade-in=0.2

# Behavior
show-failed-attempts
show-keyboard-layout
indicator-caps-lock

# Font
font=Inter
font-size=24
EOF
```

### Configure Swayidle (Idle Management)
```bash
mkdir -p ~/.config/swayidle

cat > ~/.config/swayidle/config << 'EOF'
# Swayidle configuration

# Lock screen after 5 minutes of inactivity
timeout 300 'swaylock -f'

# Turn off displays after 10 minutes of inactivity
timeout 600 'wlr-randr --output "*" --off'

# Turn displays back on when activity resumes
resume 'wlr-randr --output "*" --on'

# Lock screen before suspend
before-sleep 'swaylock -f'
EOF
```

### Set Up Wallpaper
```bash
# Create Pictures directory if it doesn't exist
mkdir -p ~/Pictures

# Create wallpaper script
cat > ~/.local/bin/set-wallpaper << 'EOF'
#!/bin/bash
# Simple wallpaper setter script

WALLPAPER_DIR="$HOME/Pictures"
DEFAULT_WALLPAPER="$WALLPAPER_DIR/wallpaper.jpg"

if [ -f "$DEFAULT_WALLPAPER" ]; then
    swaybg -i "$DEFAULT_WALLPAPER" &
else
    # Fallback to solid color
    swaybg -c "#1a1a1a" &
fi
EOF

chmod +x ~/.local/bin/set-wallpaper

# Download a sample wallpaper (optional)
curl -L -o ~/Pictures/wallpaper.jpg \
  "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2560&q=80"
```

---

## 4. Add Desktop Essentials to Hyprland

### Update Hyprland Configuration
```bash
# Add desktop essentials to Hyprland autostart
cat >> ~/.config/hypr/hyprland.conf << 'EOF'

# Desktop essentials autostart
exec-once = mako                             # Notification daemon
exec-once = nm-applet --indicator            # Network manager
exec-once = ~/.local/bin/set-wallpaper       # Wallpaper
exec-once = swayidle -w                      # Idle management

# Desktop essentials key bindings
bind = $mainMod, R, exec, wofi --show drun   # Application launcher
bind = $mainMod, L, exec, swaylock           # Lock screen
EOF
```

---

## 5. Test Desktop Essentials

### Test Each Component
```bash
# Test application launcher
wofi --show drun

# Test notifications
notify-send "Test" "Desktop essentials are working!"

# Test wallpaper
~/.local/bin/set-wallpaper

# Test screen lock (be ready to unlock!)
swaylock

# Test network manager
nm-applet --indicator &
```

---

## 6. Next Steps

After completing this guide, you have all the essential desktop tools configured. Now **CHOOSE ONE** desktop shell (skip the others):

- **Option A - QuickShell**: Modern Qt6-based desktop shell → Complete `08a-quickshell-desktop.md`
- **Option B - Waybar**: Traditional status bar approach → Complete `08b-waybar-desktop.md`

**Important**: Choose only ONE option (8a OR 8b), then proceed to `08-additional-software.md`

---

*Desktop essentials guide for Arch Linux 2025 | Common Wayland Tools*