# Non-quickshell Setup
## Traditional Status Bar for Hyprland

This guide sets up Waybar as a traditional status bar alternative to QuickShell for your Hyprland desktop.

**Prerequisites**: 
- Complete `01-arch-base-installation.md` 
- Complete `02-post-install-hardware-setup.md`
- Complete `04-display-manager-greetd.md`
- Complete `05-wayland-essentials.md`
- Complete `06-hyprland-setup.md`
- Complete `07-desktop-essentials.md`

**IMPORTANT**: This is **Option B** - Choose ONLY this option OR 8a OR 8c (not multiple)

---

## 1. Why Waybar?

**Waybar advantages**:
- **Traditional status bar**: Familiar top/bottom panel layout
- **Highly configurable**: JSON configuration with CSS styling
- **Lightweight**: Minimal resource usage
- **Wayland-native**: Built specifically for Wayland compositors
- **Rich modules**: Built-in support for many system components
- **Active community**: Extensive documentation and examples
- **Stable**: Mature and well-tested

**Choose Waybar if you prefer**:
- Traditional desktop layouts
- Simple configuration files
- Proven stability
- Extensive module ecosystem

---

## 2. Install Waybar

### Install Waybar
```bash
sudo pacman -S waybar                        # Status bar for Wayland
```

---

## 3. Configure Waybar

### Create Waybar Configuration Directory
```bash
mkdir -p ~/.config/waybar                    # Create config directory
```

### Waybar Configuration
```bash
cat > ~/.config/waybar/config << 'EOF'
{
    "layer": "top",
    "position": "top", 
    "height": 30,
    "spacing": 4,
    
    "modules-left": ["hyprland/workspaces"],
    "modules-center": ["clock"],
    "modules-right": ["pulseaudio", "network", "battery", "tray"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "all-outputs": true,
        "format": "{name}: {icon}",
        "format-icons": {
            "1": "",
            "2": "",
            "3": "",
            "4": "",
            "5": "",
            "urgent": "",
            "focused": "",
            "default": ""
        }
    },
    
    "clock": {
        "timezone": "Europe/London",
        "tooltip-format": "<big>{:%Y %B}</big>\n<tt><small>{calendar}</small></tt>",
        "format-alt": "{:%Y-%m-%d}"
    },
    
    "pulseaudio": {
        "scroll-step": 1,
        "format": "{volume}% {icon} {format_source}",
        "format-bluetooth": "{volume}% {icon} {format_source}",
        "format-bluetooth-muted": " {icon} {format_source}",
        "format-muted": " {format_source}",
        "format-source": "{volume}% ",
        "format-source-muted": "",
        "format-icons": {
            "headphone": "",
            "hands-free": "",
            "headset": "",
            "phone": "",
            "portable": "",
            "car": "",
            "default": ["", "", ""]
        },
        "on-click": "pavucontrol"
    },
    
    "network": {
        "format-wifi": "{essid} ({signalStrength}%) ",
        "format-ethernet": "{ipaddr}/{cidr} ",
        "tooltip-format": "{ifname} via {gwaddr} ",
        "format-linked": "{ifname} (No IP) ",
        "format-disconnected": "Disconnected ⚠",
        "format-alt": "{ifname}: {ipaddr}/{cidr}"
    },
    
    "tray": {
        "icon-size": 21,
        "spacing": 10
    }
}
EOF
```

### Waybar Styling
```bash
cat > ~/.config/waybar/style.css << 'EOF'
* {
    border: none;
    border-radius: 0;
    font-family: "Roboto, Helvetica, Arial, sans-serif";
    font-size: 13px;
    min-height: 0;
}

window#waybar {
    background-color: rgba(43, 48, 59, 0.95);
    border-bottom: 3px solid rgba(100, 114, 125, 0.5);
    color: #ffffff;
    transition-property: background-color;
    transition-duration: .5s;
}

#workspaces button {
    padding: 0 5px;
    background-color: transparent;
    color: #ffffff;
    border-bottom: 3px solid transparent;
}

#workspaces button:hover {
    background: rgba(0, 0, 0, 0.2);
    box-shadow: inset 0 -3px #ffffff;
}

#workspaces button.focused {
    background-color: #64727D;
    border-bottom: 3px solid #ffffff;
}

#clock, #pulseaudio, #network, #tray {
    padding: 0 10px;
    color: #ffffff;
}

#pulseaudio {
    color: #74c7ec;
}

#network {
    color: #a6e3a1;
}

#network.disconnected {
    color: #f38ba8;
}

#clock {
    color: #fab387;
}
EOF
```

---

## 4. Advanced Waybar Configuration

### Extended Configuration with More Modules
```bash
cat > ~/.config/waybar/config << 'EOF'
{
    "layer": "top",
    "position": "top",
    "height": 32,
    "spacing": 4,
    
    "modules-left": ["hyprland/workspaces", "hyprland/window"],
    "modules-center": ["clock"],
    "modules-right": ["cpu", "memory", "temperature", "pulseaudio", "network", "battery", "tray"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "all-outputs": true,
        "format": "{name}: {icon}",
        "format-icons": {
            "1": "",
            "2": "",
            "3": "",
            "4": "",
            "5": "",
            "6": "",
            "7": "",
            "8": "",
            "9": "",
            "10": "",
            "urgent": "",
            "focused": "",
            "default": ""
        }
    },
    
    "hyprland/window": {
        "format": "{}",
        "max-length": 50,
        "separate-outputs": true
    },
    
    "clock": {
        "timezone": "Europe/London",
        "format": "{:%H:%M}",
        "format-alt": "{:%Y-%m-%d %H:%M:%S}",
        "tooltip-format": "<big>{:%Y %B}</big>\n<tt><small>{calendar}</small></tt>"
    },
    
    "cpu": {
        "format": "{usage}% ",
        "tooltip": false,
        "on-click": "kitty -e htop"
    },
    
    "memory": {
        "format": "{}% ",
        "on-click": "kitty -e htop"
    },
    
    "temperature": {
        "thermal-zone": 2,
        "hwmon-path": "/sys/class/hwmon/hwmon2/temp1_input",
        "critical-threshold": 80,
        "format-critical": "{temperatureC}°C {icon}",
        "format": "{temperatureC}°C {icon}",
        "format-icons": ["", "", ""]
    },
    
    "pulseaudio": {
        "scroll-step": 5,
        "format": "{volume}% {icon} {format_source}",
        "format-bluetooth": "{volume}% {icon} {format_source}",
        "format-bluetooth-muted": " {icon} {format_source}",
        "format-muted": " {format_source}",
        "format-source": "{volume}% ",
        "format-source-muted": "",
        "format-icons": {
            "headphone": "",
            "hands-free": "",
            "headset": "",
            "phone": "",
            "portable": "",
            "car": "",
            "default": ["", "", ""]
        },
        "on-click": "pavucontrol",
        "on-click-right": "pamixer -t"
    },
    
    "network": {
        "format-wifi": "{essid} ({signalStrength}%) ",
        "format-ethernet": "{ipaddr}/{cidr} ",
        "tooltip-format": "{ifname} via {gwaddr} ",
        "format-linked": "{ifname} (No IP) ",
        "format-disconnected": "Disconnected ⚠",
        "format-alt": "{ifname}: {ipaddr}/{cidr}",
        "on-click": "nm-connection-editor"
    },
    
    "battery": {
        "states": {
            "warning": 30,
            "critical": 15
        },
        "format": "{capacity}% {icon}",
        "format-charging": "{capacity}% ",
        "format-plugged": "{capacity}% ",
        "format-alt": "{time} {icon}",
        "format-icons": ["", "", "", "", ""]
    },
    
    "tray": {
        "icon-size": 21,
        "spacing": 10
    }
}
EOF
```

### Enhanced Styling
```bash
cat > ~/.config/waybar/style.css << 'EOF'
* {
    border: none;
    border-radius: 0;
    font-family: "JetBrains Mono", "Font Awesome 6 Free", monospace;
    font-size: 13px;
    min-height: 0;
}

window#waybar {
    background-color: rgba(26, 26, 26, 0.9);
    border-bottom: 2px solid #33ccff;
    color: #ffffff;
    transition-property: background-color;
    transition-duration: 0.5s;
}

window#waybar.hidden {
    opacity: 0.2;
}

#workspaces {
    margin: 0 4px;
}

#workspaces button {
    padding: 0 8px;
    background-color: transparent;
    color: #ffffff;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

#workspaces button:hover {
    background: rgba(51, 204, 255, 0.2);
    border-bottom: 2px solid #33ccff;
}

#workspaces button.active {
    background-color: #33ccff;
    color: #000000;
    border-bottom: 2px solid #33ccff;
}

#workspaces button.urgent {
    background-color: #ff6b6b;
    color: #ffffff;
    border-bottom: 2px solid #ff6b6b;
}

#window {
    margin: 0 4px;
    padding: 0 8px;
    color: #cccccc;
    font-style: italic;
}

#clock {
    margin: 0 4px;
    padding: 0 8px;
    color: #fab387;
    font-weight: bold;
}

#cpu {
    margin: 0 2px;
    padding: 0 6px;
    color: #74c7ec;
}

#memory {
    margin: 0 2px;
    padding: 0 6px;
    color: #a6e3a1;
}

#temperature {
    margin: 0 2px;
    padding: 0 6px;
    color: #f9e2af;
}

#temperature.critical {
    color: #ff6b6b;
    animation: blink 1s linear infinite;
}

@keyframes blink {
    to {
        background-color: #ff6b6b;
        color: #000000;
    }
}

#pulseaudio {
    margin: 0 2px;
    padding: 0 6px;
    color: #cba6f7;
}

#pulseaudio.muted {
    color: #888888;
}

#network {
    margin: 0 2px;
    padding: 0 6px;
    color: #a6e3a1;
}

#network.disconnected {
    color: #ff6b6b;
}

#battery {
    margin: 0 2px;
    padding: 0 6px;
    color: #a6e3a1;
}

#battery.charging {
    color: #f9e2af;
}

#battery.warning:not(.charging) {
    color: #fab387;
}

#battery.critical:not(.charging) {
    color: #ff6b6b;
    animation: blink 1s linear infinite;
}

#tray {
    margin: 0 4px;
    padding: 0 4px;
}

#tray > .passive {
    -gtk-icon-effect: dim;
}

#tray > .needs-attention {
    -gtk-icon-effect: highlight;
    background-color: #ff6b6b;
}

tooltip {
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid #33ccff;
    border-radius: 5px;
    color: #ffffff;
}

tooltip label {
    color: #ffffff;
}
EOF
```

---

## 5. Auto-start Waybar with Hyprland

### Add Waybar to Hyprland Configuration
```bash
# Add Waybar to Hyprland autostart (alternative to QuickShell)
echo 'exec-once = waybar' >> ~/.config/hypr/hyprland.conf
```

**Important**: Choose either QuickShell OR Waybar - don't run both simultaneously.

---

## 6. Waybar Control Scripts

### Create Waybar Control Script
```bash
mkdir -p ~/.config/hypr/scripts

cat > ~/.config/hypr/scripts/waybar-control.sh << 'EOF'
#!/bin/bash
# Waybar control script

case "$1" in
    start)
        echo "Starting Waybar..."
        waybar &
        ;;
    stop)
        echo "Stopping Waybar..."
        pkill waybar
        ;;
    restart)
        echo "Restarting Waybar..."
        pkill waybar
        sleep 1
        waybar &
        ;;
    reload)
        echo "Reloading Waybar configuration..."
        pkill -SIGUSR2 waybar
        ;;
    toggle)
        if pgrep waybar > /dev/null; then
            pkill waybar
            echo "Waybar hidden"
        else
            waybar &
            echo "Waybar shown"
        fi
        ;;
    status)
        if pgrep waybar > /dev/null; then
            echo "Waybar is running"
        else
            echo "Waybar is not running"
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|reload|toggle|status}"
        exit 1
        ;;
esac
EOF

chmod +x ~/.config/hypr/scripts/waybar-control.sh
```

### Add Hyprland Key Bindings for Waybar
```bash
cat >> ~/.config/hypr/hyprland.conf << 'EOF'

# Waybar controls
bind = $mainMod SHIFT, W, exec, ~/.config/hypr/scripts/waybar-control.sh restart
bind = $mainMod CTRL, W, exec, ~/.config/hypr/scripts/waybar-control.sh reload
bind = $mainMod ALT, W, exec, ~/.config/hypr/scripts/waybar-control.sh toggle
EOF
```

---

## 7. Custom Waybar Modules

### Create Custom Scripts Directory
```bash
mkdir -p ~/.config/waybar/scripts
```

### GPU Temperature Script (NVIDIA)
```bash
cat > ~/.config/waybar/scripts/gpu-temp.sh << 'EOF'
#!/bin/bash
# GPU temperature script for Waybar

if command -v nvidia-smi &> /dev/null; then
    temp=$(nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits)
    usage=$(nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits)
    
    if [ "$temp" -gt 80 ]; then
        class="critical"
    elif [ "$temp" -gt 70 ]; then
        class="warning"
    else
        class="normal"
    fi
    
    echo "{\"text\":\"${temp}°C\", \"tooltip\":\"GPU: ${usage}% usage\", \"class\":\"$class\"}"
else
    echo "{\"text\":\"N/A\", \"tooltip\":\"NVIDIA GPU not found\"}"
fi
EOF

chmod +x ~/.config/waybar/scripts/gpu-temp.sh
```

### Add GPU Module to Waybar Config
```bash
# Add this to your waybar config modules-right array:
# "custom/gpu"

# Add this to your waybar config:
cat >> ~/.config/waybar/config << 'EOF'
    
    "custom/gpu": {
        "exec": "~/.config/waybar/scripts/gpu-temp.sh",
        "return-type": "json",
        "interval": 5,
        "tooltip": true,
        "on-click": "nvidia-settings"
    }
EOF
```

### Weather Module Script
```bash
cat > ~/.config/waybar/scripts/weather.sh << 'EOF'
#!/bin/bash
# Weather script for Waybar

LOCATION="London"
WEATHER=$(curl -s "wttr.in/${LOCATION}?format=%C+%t" 2>/dev/null)

if [ $? -eq 0 ] && [ -n "$WEATHER" ]; then
    echo "{\"text\":\"$WEATHER\", \"tooltip\":\"Weather in $LOCATION\"}"
else
    echo "{\"text\":\"Weather N/A\", \"tooltip\":\"Unable to fetch weather\"}"
fi
EOF

chmod +x ~/.config/waybar/scripts/weather.sh
```

---

## 8. Multiple Waybar Configurations

### Create Multiple Config Files
```bash
# Gaming configuration (minimal)
cat > ~/.config/waybar/config-gaming << 'EOF'
{
    "layer": "top",
    "position": "top",
    "height": 24,
    "spacing": 2,
    
    "modules-left": ["hyprland/workspaces"],
    "modules-center": [],
    "modules-right": ["clock"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "format": "{icon}",
        "format-icons": {
            "default": ""
        }
    },
    
    "clock": {
        "format": "{:%H:%M}",
        "tooltip": false
    }
}
EOF

# Work configuration (detailed)
cat > ~/.config/waybar/config-work << 'EOF'
{
    "layer": "top",
    "position": "top",
    "height": 36,
    "spacing": 6,
    
    "modules-left": ["hyprland/workspaces", "hyprland/window"],
    "modules-center": ["clock"],
    "modules-right": ["cpu", "memory", "custom/gpu", "pulseaudio", "network", "battery", "tray"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "all-outputs": true,
        "format": "{name}: {icon}",
        "format-icons": {
            "1": "",
            "2": "",
            "3": "",
            "4": "",
            "5": "",
            "default": ""
        }
    },
    
    "hyprland/window": {
        "format": "{}",
        "max-length": 60
    },
    
    "clock": {
        "format": "{:%H:%M:%S}",
        "format-alt": "{:%Y-%m-%d %H:%M:%S}",
        "interval": 1,
        "tooltip-format": "<big>{:%Y %B}</big>\n<tt><small>{calendar}</small></tt>"
    },
    
    "cpu": {
        "format": "{usage}% ",
        "on-click": "kitty -e htop"
    },
    
    "memory": {
        "format": "{}% ",
        "on-click": "kitty -e htop"
    },
    
    "custom/gpu": {
        "exec": "~/.config/waybar/scripts/gpu-temp.sh",
        "return-type": "json",
        "interval": 5,
        "on-click": "nvidia-settings"
    },
    
    "pulseaudio": {
        "format": "{volume}% {icon}",
        "format-icons": ["", "", ""],
        "on-click": "pavucontrol"
    },
    
    "network": {
        "format-wifi": "{essid} ",
        "format-ethernet": " ",
        "format-disconnected": "⚠",
        "on-click": "nm-connection-editor"
    },
    
    "battery": {
        "format": "{capacity}% {icon}",
        "format-icons": ["", "", "", "", ""]
    },
    
    "tray": {
        "icon-size": 18,
        "spacing": 8
    }
}
EOF
```

### Configuration Switching Script
```bash
cat > ~/.config/waybar/scripts/switch-config.sh << 'EOF'
#!/bin/bash
# Waybar configuration switcher

CONFIG_DIR="$HOME/.config/waybar"
CURRENT_CONFIG="$CONFIG_DIR/config"

case "$1" in
    gaming)
        cp "$CONFIG_DIR/config-gaming" "$CURRENT_CONFIG"
        echo "Switched to gaming configuration"
        ;;
    work)
        cp "$CONFIG_DIR/config-work" "$CURRENT_CONFIG"
        echo "Switched to work configuration"
        ;;
    default)
        cp "$CONFIG_DIR/config-default" "$CURRENT_CONFIG" 2>/dev/null || echo "Default config not found"
        echo "Switched to default configuration"
        ;;
    *)
        echo "Usage: $0 {gaming|work|default}"
        echo "Current configurations available:"
        ls -1 "$CONFIG_DIR"/config-* 2>/dev/null | sed 's/.*config-/  /'
        exit 1
        ;;
esac

# Restart waybar to apply changes
pkill waybar
sleep 1
waybar &
EOF

chmod +x ~/.config/waybar/scripts/switch-config.sh
```

---

## 9. Troubleshooting and Debugging

### Debug Waybar Issues
```bash
# Run Waybar with debug output
waybar -l debug

# Check Waybar logs
journalctl --user -f | grep waybar

# Validate JSON configuration
python -m json.tool ~/.config/waybar/config
```

### Common Issues and Solutions

**Issue: Waybar won't start**
```bash
# Check configuration syntax
python -m json.tool ~/.config/waybar/config

# Verify dependencies
pacman -Q waybar

# Check for conflicting processes
ps aux | grep waybar
```

**Issue: Modules not working**
```bash
# Check if required commands are available
which pamixer
which nmcli
which nvidia-smi

# Test module scripts individually
~/.config/waybar/scripts/gpu-temp.sh
```

**Issue: Styling not applied**
```bash
# Check CSS syntax
# Validate CSS file manually

# Restart waybar
pkill waybar && waybar &
```

---

## ✅ Waybar Desktop Complete!

Your Waybar desktop environment now includes:
- ✅ Traditional status bar layout
- ✅ System monitoring modules
- ✅ Custom scripts and modules
- ✅ Multiple configuration profiles
- ✅ Hyprland integration
- ✅ Control scripts for management

## Next Steps

Continue with:
1. **Additional Software**: `08-additional-software.md` - Install applications and tools

## Usage Tips

- **Super + Shift + W**: Restart Waybar
- **Super + Ctrl + W**: Reload Waybar configuration
- **Super + Alt + W**: Toggle Waybar visibility
- **Click modules**: Most modules are interactive
- **Switch configs**: Use the configuration switcher script

## Customization Ideas

- Add custom modules for specific applications
- Create time-based configuration switching
- Implement workspace-specific styling
- Add system notification integration
- Create custom weather and system monitoring

---

*Waybar desktop guide for Arch Linux 2025 | Traditional Status Bar + Hyprland*