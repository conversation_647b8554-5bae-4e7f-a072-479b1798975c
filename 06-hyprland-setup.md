# Hyprland Setup
## Modern Wayland Compositor Configuration

This guide sets up <PERSON><PERSON><PERSON><PERSON>, a modern tiling Wayland compositor with advanced features and smooth animations.

**Prerequisites**: 
- Complete `01-arch-base-installation.md` 
- Complete `02-post-install-hardware-setup.md`
- Complete `04-display-manager-greetd.md`
- Complete `05-wayland-essentials.md`
- Optionally complete `03-backup-with-urbackup.md` (**OPTIONAL** - can be skipped)

---

## 1. Why Hyprland?

**Hyprland advantages**:
- **Modern Wayland compositor**: Built specifically for Wayland protocol
- **Tiling window manager**: Automatic window arrangement and management
- **Smooth animations**: GPU-accelerated animations and effects
- **Highly configurable**: Extensive customization options
- **Active development**: Regular updates and new features
- **NVIDIA support**: Works well with NVIDIA GPUs
- **Multi-monitor**: Excellent multi-monitor support
- **Plugin system**: Extensible with plugins

---

## 2. Install Hyprland

### Install Hyprland and Dependencies
```bash
sudo pacman -S hyprland \                    # Hyprland compositor
               xdg-desktop-portal-hyprland \ # Desktop portal for Hyprland
               polkit-kde-agent \            # Authentication agent
               qt5-wayland \                 # Qt5 Wayland support
               qt6-wayland                   # Qt6 Wayland support
```

**Package explanations**:
- `hyprland`: The main compositor
- `xdg-desktop-portal-hyprland`: Handles desktop integration (file dialogs, screen sharing)
- `polkit-kde-agent`: Handles authentication prompts
- `qt5-wayland` & `qt6-wayland`: Qt application Wayland support

---

## 3. Basic Hyprland Configuration

### Create Hyprland Configuration Directory
```bash
mkdir -p ~/.config/hypr                      # Create config directory
```

### Basic Hyprland Configuration
```bash
cat > ~/.config/hypr/hyprland.conf << 'EOF'
# Hyprland Configuration
# See https://wiki.hyprland.org/Configuring/Configuring-Hyprland/

# Monitor configuration
monitor=,preferred,auto,auto

# Execute your favorite apps at launch
exec-once = waybar
exec-once = hyprpaper
exec-once = /usr/lib/polkit-kde-authentication-agent-1

# Source a file (multi-file configs)
# source = ~/.config/hypr/myColors.conf

# Set programs that you use
$terminal = foot
$fileManager = thunar
$menu = wofi --show drun

# Some default env vars
env = XCURSOR_SIZE,24
env = QT_QPA_PLATFORM,wayland
env = XDG_CURRENT_DESKTOP,Hyprland
env = XDG_SESSION_TYPE,wayland
env = XDG_SESSION_DESKTOP,Hyprland

# For all categories, see https://wiki.hyprland.org/Configuring/Variables/
input {
    kb_layout = gb
    kb_variant =
    kb_model =
    kb_options =
    kb_rules =

    follow_mouse = 1

    touchpad {
        natural_scroll = no
    }

    sensitivity = 0 # -1.0 - 1.0, 0 means no modification.
}

general {
    # See https://wiki.hyprland.org/Configuring/Variables/ for more

    gaps_in = 5
    gaps_out = 20
    border_size = 2
    col.active_border = rgba(33ccffee) rgba(00ff99ee) 45deg
    col.inactive_border = rgba(595959aa)

    layout = dwindle

    # Please see https://wiki.hyprland.org/Configuring/Tearing/ before you turn this on
    allow_tearing = false
}

decoration {
    # See https://wiki.hyprland.org/Configuring/Variables/ for more

    rounding = 10
    
    blur {
        enabled = true
        size = 3
        passes = 1
    }

    drop_shadow = yes
    shadow_range = 4
    shadow_render_power = 3
    col.shadow = rgba(1a1a1aee)
}

animations {
    enabled = yes

    # Some default animations, see https://wiki.hyprland.org/Configuring/Animations/ for more

    bezier = myBezier, 0.05, 0.9, 0.1, 1.05

    animation = windows, 1, 7, myBezier
    animation = windowsOut, 1, 7, default, popin 80%
    animation = border, 1, 10, default
    animation = borderangle, 1, 8, default
    animation = fade, 1, 7, default
    animation = workspaces, 1, 6, default
}

dwindle {
    # See https://wiki.hyprland.org/Configuring/Dwindle-Layout/ for more
    pseudotile = yes # master switch for pseudotiling. Enabling is bound to mainMod + P in the keybinds section below
    preserve_split = yes # you probably want this
}

master {
    # See https://wiki.hyprland.org/Configuring/Master-Layout/ for more
    new_is_master = true
}

gestures {
    # See https://wiki.hyprland.org/Configuring/Variables/ for more
    workspace_swipe = off
}

misc {
    # See https://wiki.hyprland.org/Configuring/Variables/ for more
    force_default_wallpaper = -1 # Set to 0 to disable the anime mascot wallpapers
}

# Example per-device config
# See https://wiki.hyprland.org/Configuring/Keywords/#executing for more
device:epic-mouse-v1 {
    sensitivity = -0.5
}

# Example windowrule v1
# windowrule = float, ^(kitty)$
# Example windowrule v2
# windowrulev2 = float,class:^(kitty)$,title:^(kitty)$
# See https://wiki.hyprland.org/Configuring/Window-Rules/ for more

# See https://wiki.hyprland.org/Configuring/Keywords/ for more
$mainMod = SUPER

# Example binds, see https://wiki.hyprland.org/Configuring/Binds/ for more
bind = $mainMod, Q, exec, $terminal
bind = $mainMod, C, killactive, 
bind = $mainMod, M, exit, 
bind = $mainMod, E, exec, $fileManager
bind = $mainMod, V, togglefloating, 
bind = $mainMod, R, exec, $menu
bind = $mainMod, P, pseudo, # dwindle
bind = $mainMod, J, togglesplit, # dwindle

# Move focus with mainMod + arrow keys
bind = $mainMod, left, movefocus, l
bind = $mainMod, right, movefocus, r
bind = $mainMod, up, movefocus, u
bind = $mainMod, down, movefocus, d

# Switch workspaces with mainMod + [0-9]
bind = $mainMod, 1, workspace, 1
bind = $mainMod, 2, workspace, 2
bind = $mainMod, 3, workspace, 3
bind = $mainMod, 4, workspace, 4
bind = $mainMod, 5, workspace, 5
bind = $mainMod, 6, workspace, 6
bind = $mainMod, 7, workspace, 7
bind = $mainMod, 8, workspace, 8
bind = $mainMod, 9, workspace, 9
bind = $mainMod, 0, workspace, 10

# Move active window to a workspace with mainMod + SHIFT + [0-9]
bind = $mainMod SHIFT, 1, movetoworkspace, 1
bind = $mainMod SHIFT, 2, movetoworkspace, 2
bind = $mainMod SHIFT, 3, movetoworkspace, 3
bind = $mainMod SHIFT, 4, movetoworkspace, 4
bind = $mainMod SHIFT, 5, movetoworkspace, 5
bind = $mainMod SHIFT, 6, movetoworkspace, 6
bind = $mainMod SHIFT, 7, movetoworkspace, 7
bind = $mainMod SHIFT, 8, movetoworkspace, 8
bind = $mainMod SHIFT, 9, movetoworkspace, 9
bind = $mainMod SHIFT, 0, movetoworkspace, 10

# Example special workspace (scratchpad)
bind = $mainMod, S, togglespecialworkspace, magic
bind = $mainMod SHIFT, S, movetoworkspace, special:magic

# Scroll through existing workspaces with mainMod + scroll
bind = $mainMod, mouse_down, workspace, e+1
bind = $mainMod, mouse_up, workspace, e-1

# Move/resize windows with mainMod + LMB/RMB and dragging
bindm = $mainMod, mouse:272, movewindow
bindm = $mainMod, mouse:273, resizewindow

# Screenshot binds
bind = , Print, exec, grim -g "$(slurp)" - | wl-copy
bind = SHIFT, Print, exec, grim ~/Pictures/screenshot-$(date +%Y%m%d-%H%M%S).png

# Volume and brightness controls
bind = , XF86AudioRaiseVolume, exec, pamixer -i 5
bind = , XF86AudioLowerVolume, exec, pamixer -d 5
bind = , XF86AudioMute, exec, pamixer -t
bind = , XF86MonBrightnessUp, exec, brightnessctl set +5%
bind = , XF86MonBrightnessDown, exec, brightnessctl set 5%-

# Media controls
bind = , XF86AudioPlay, exec, playerctl play-pause
bind = , XF86AudioNext, exec, playerctl next
bind = , XF86AudioPrev, exec, playerctl previous
EOF
```

---

## 4. Configure Greetd for Hyprland

### Update Greetd Configuration
```bash
sudo nano /etc/greetd/config.toml
```

Make sure the default session section looks like this:
```toml
[default_session]
command = "tuigreet --time --cmd Hyprland"
user = "greeter"
```

---

## 5. NVIDIA-Specific Configuration (If Using NVIDIA)

### NVIDIA Environment Variables
```bash
cat >> ~/.config/hypr/hyprland.conf << 'EOF'

# NVIDIA-specific settings
env = LIBVA_DRIVER_NAME,nvidia
env = XDG_SESSION_TYPE,wayland
env = GBM_BACKEND,nvidia-drm
env = __GLX_VENDOR_LIBRARY_NAME,nvidia
env = WLR_NO_HARDWARE_CURSORS,1
EOF
```

---

## 6. Test Hyprland

### Start Hyprland Session
```bash
# Reboot to test with greetd
sudo reboot

# Or test manually (from TTY)
# Hyprland
```

### Basic Hyprland Shortcuts
- **Super + Q**: Open terminal
- **Super + C**: Close window
- **Super + R**: Open application launcher
- **Super + E**: Open file manager
- **Super + 1-9**: Switch workspaces
- **Super + Shift + 1-9**: Move window to workspace
- **Super + Mouse**: Move/resize windows
- **Print**: Screenshot selection
- **Shift + Print**: Full screenshot

---

## 7. Next Steps

After Hyprland is working, continue with:
- **Desktop Essentials**: Complete `07-desktop-essentials.md`
- **Choose Desktop Shell**: Pick ONE of:
  - `08a-quickshell-desktop.md` (Modern Qt-based)
  - `08b-waybar-desktop.md` (Traditional status bar)
  - `08c-hybrid-desktop-setup.md` (Combination approach)

---

## 8. Troubleshooting

### Common Issues

**Hyprland won't start**:
```bash
# Check logs
journalctl -u greetd
# Or start manually to see errors
Hyprland
```

**NVIDIA issues**:
```bash
# Verify NVIDIA drivers
nvidia-smi
# Check Wayland support
echo $XDG_SESSION_TYPE
```

**No audio/brightness controls**:
```bash
# Install missing packages
sudo pacman -S pamixer brightnessctl playerctl
```

---

*Hyprland setup guide for Arch Linux 2025 | Modern Wayland Compositor*