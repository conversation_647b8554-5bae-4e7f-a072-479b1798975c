# Post-Installation Hardware Setup
## Intel CPU + NVIDIA RTX 3090 + Audio Configuration

This guide configures hardware-specific drivers and optimizations after completing the base Arch installation.

**Prerequisites**: Complete `01-arch-base-installation.md` and successfully boot into your new system.

**Note**: You can skip the backup guide (`03-backup-with-urbackup.md`) if you don't need automated backups initially.

---

## 1. Post-Installation: Intel CPU Configuration

**Login as your user** (not root), then continue:

### Intel-specific Optimizations
```bash
# Intel microcode is already installed, verify it's loaded
dmesg | grep microcode                       # Should show microcode loading

# Install Intel graphics drivers (for integrated graphics)
sudo pacman -S mesa lib32-mesa               # Intel graphics drivers
sudo pacman -S intel-media-driver            # Hardware video acceleration
sudo pacman -S libva-intel-driver            # VA-API support (legacy)
```

### CPU Frequency Scaling (Intel) - **OPTIONAL**
```bash
sudo pacman -S cpupower                      # CPU frequency management
echo 'governor="performance"' | sudo tee /etc/default/cpupower
sudo systemctl enable cpupower               # Apply performance governor on boot
```

**Explanation**: Performance governor maximizes CPU performance. Use "powersave" for laptops or "ondemand" for automatic scaling.

**⚠️ OPTIONAL**: The system works fine with default CPU scaling. This is only for performance optimization.

---

## 2. NVIDIA RTX 3090 Setup (Wayland-Optimized)

### Install NVIDIA Drivers (Comprehensive Setup)
```bash
# Install NVIDIA drivers and utilities
sudo pacman -S nvidia-dkms \                 # NVIDIA drivers with DKMS
               nvidia-utils \                # NVIDIA utilities
               lib32-nvidia-utils \          # 32-bit support for gaming
               nvidia-settings \             # NVIDIA control panel
               libva-nvidia-driver \         # Hardware video acceleration
               nvtop                         # GPU monitoring tool
```

### Configure NVIDIA for Wayland/Hyprland
```bash
# Enable NVIDIA persistence daemon for better performance
sudo systemctl enable nvidia-persistenced

# Configure NVIDIA power management
echo 'options nvidia "NVreg_DynamicPowerManagement=0x02"' | sudo tee /etc/modprobe.d/nvidia.conf

# Add NVIDIA modules to initramfs (for early loading)
echo 'MODULES=(nvidia nvidia_modeset nvidia_uvm nvidia_drm)' | sudo tee -a /etc/mkinitcpio.conf

# Regenerate initramfs
sudo mkinitcpio -P
```

### Configure NVIDIA DKMS Hooks (Critical for Kernel Updates)
```bash
# Create pacman hook to rebuild NVIDIA modules on kernel updates
sudo mkdir -p /etc/pacman.d/hooks

sudo tee /etc/pacman.d/hooks/nvidia.hook << 'EOF'
[Trigger]
Operation=Install
Operation=Upgrade
Operation=Remove
Type=Package
Target=nvidia-dkms
Target=linux
Target=linux-lts

[Description]
Update NVIDIA module in initcpio

[Depends]
mkinitcpio

[When]
PostTransaction

[Exec]
/bin/sh -c 'while read -r trg; do case $trg in linux*) exit 0; esac; done; /usr/bin/mkinitcpio -P'
EOF

# Create hook for DKMS module rebuilding
sudo tee /etc/pacman.d/hooks/dkms.hook << 'EOF'
[Trigger]
Operation=Install
Operation=Upgrade
Type=Package
Target=linux
Target=linux-lts

[Description]
Update DKMS modules

[Depends]
dkms

[When]
PostTransaction

[Exec]
/usr/bin/dkms autoinstall
EOF
```

### NVIDIA Wayland Configuration
```bash
# Enable NVIDIA Wayland support
echo 'NVIDIA_WAYLAND=1' | sudo tee -a /etc/environment

# Configure kernel parameters for NVIDIA
echo 'GRUB_CMDLINE_LINUX_DEFAULT="quiet nvidia-drm.modeset=1"' | sudo tee -a /etc/default/grub
sudo grub-mkconfig -o /boot/grub/grub.cfg
```

### Test NVIDIA Installation
```bash
nvidia-smi                                   # Should show GPU info
# Note: nvidia-settings requires a running desktop environment - test after installing Hyprland/desktop
```

**Reboot after NVIDIA setup**:
```bash
sudo reboot
```

---

## 3. Audio System Setup (PipeWire)

### Install PipeWire Audio Stack
```bash
# Audio system (modern replacement for PulseAudio)
sudo pacman -S pipewire \                    # Modern audio server
               pipewire-pulse \              # PulseAudio compatibility
               pipewire-jack \               # JACK compatibility
               pipewire-alsa \               # ALSA support
               wireplumber                   # Session manager

# Essential desktop tools
sudo pacman -S xdg-utils \                   # Desktop integration
               xdg-user-dirs \               # User directories
               polkit-kde-agent \            # Authentication agent
               qt5-wayland \                 # Qt5 Wayland support
               qt6-wayland                   # Qt6 Wayland support

# Terminal emulators (choose one or more)
sudo pacman -S kitty \                       # Modern GPU-accelerated terminal
               alacritty \                   # Rust-based fast terminal
               foot                          # Lightweight Wayland terminal

# File managers (choose one)
sudo pacman -S thunar \                      # GTK file manager
               dolphin \                     # KDE file manager
               nautilus                      # GNOME file manager
```

### Enable Audio Services
```bash
systemctl --user enable pipewire             # Enable audio for user
systemctl --user enable pipewire-pulse       # Enable PulseAudio compatibility
systemctl --user enable wireplumber          # Enable session manager
```

---

## 4. Bluetooth Setup - **OPTIONAL**

### Install Bluetooth Packages
```bash
sudo pacman -S bluez bluez-utils bluez-tools  # Bluetooth stack and utilities
sudo pacman -S blueman                        # Bluetooth manager GUI (optional)
```

### Configure Bluetooth
```bash
# Enable Bluetooth service
sudo systemctl enable bluetooth
sudo systemctl start bluetooth

# Add user to bluetooth group
sudo usermod -aG bluetooth Administrator

# Configure auto-power-on
sudo sed -i 's/#AutoEnable=false/AutoEnable=true/' /etc/bluetooth/main.conf
```

### Test Bluetooth
```bash
bluetoothctl                                 # Bluetooth control utility
# In bluetoothctl:
# power on
# agent on
# default-agent
# scan on
# pair [device_mac]
# connect [device_mac]
```

---

## 5. Font Installation

### Install Essential Fonts
```bash
# Core fonts for good desktop experience
sudo pacman -S noto-fonts \                 # Google Noto fonts (comprehensive)
               noto-fonts-emoji \            # Emoji support
               ttf-dejavu \                  # DejaVu fonts
               ttf-liberation \              # Liberation fonts (MS Office compatible)
               ttf-opensans \                # Open Sans
               adobe-source-code-pro-fonts \ # Source Code Pro (programming)
               ttf-fira-code \               # Fira Code (programming with ligatures)
               ttf-font-awesome              # Font Awesome icons

# Additional fonts (optional)
sudo pacman -S ttf-roboto \                 # Roboto font family
               ttf-ubuntu-font-family \      # Ubuntu fonts
               noto-fonts-cjk               # Chinese, Japanese, Korean support
```

### Update Font Cache
```bash
fc-cache -fv                                # Rebuild font cache
```

---

## 6. System Optimization & Performance

### CPU Scaling and Performance
```bash
# For desktop systems, performance governor is usually better
echo 'governor="performance"' | sudo tee /etc/default/cpupower
sudo systemctl enable cpupower
```

### Memory & I/O Optimization
```bash
# Configure swappiness (lower = less swap usage)
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.d/99-swappiness.conf

# Configure I/O scheduler for SSDs
echo 'ACTION=="add|change", KERNEL=="nvme[0-9]*", ATTR{queue/scheduler}="none"' | sudo tee /etc/udev/rules.d/60-ioschedulers.rules
echo 'ACTION=="add|change", KERNEL=="sd[a-z]|mmcblk[0-9]*", ATTR{queue/rotational}=="0", ATTR{queue/scheduler}="mq-deadline"' | sudo tee -a /etc/udev/rules.d/60-ioschedulers.rules
```

### NVIDIA Performance Optimization - **OPTIONAL**
```bash
# Configure NVIDIA power management
echo '__GL_SHADER_DISK_CACHE_PATH=/tmp' | sudo tee -a /etc/environment

# Note: For Wayland/Hyprland, overclocking is configured via kernel parameters, not nvidia-xconfig
```

---

## 7. Enable Multilib (32-bit support)

```bash
sudo nano /etc/pacman.conf
```

Uncomment these lines:
```ini
[multilib]
Include = /etc/pacman.d/mirrorlist
```

Then update:
```bash
sudo pacman -Syy                             # Refresh with multilib
```

---

## 8. AUR Helper Installation

### Install Paru (Recommended AUR Helper)
```bash
# Clone and build paru
cd /tmp
git clone https://aur.archlinux.org/paru.git
cd paru
makepkg -si --noconfirm

# Configure paru
mkdir -p ~/.config/paru
tee ~/.config/paru/paru.conf << 'EOF'
[options]
PgpFetch
Devel
Provides
DevelSuffixes = -git -cvs -svn -bzr -darcs -always
BottomUp
RemoveMake
SudoLoop
UseAsk
CombinedUpgrade
CleanAfter
UpgradeMenu
NewsOnUpgrade
EOF
```

### Using Paru
```bash
# Search AUR packages
paru -Ss package_name

# Install AUR packages
paru -S package_name

# Update all packages (official + AUR)
paru -Syu

# Update only AUR packages
paru -Sua
```

**Explanation**: While Chaotic-AUR provides many pre-compiled AUR packages, paru is essential for packages not available there and for long-term AUR management.

---

## ✅ Hardware Setup Complete!

Your system now has:
- ✅ Intel CPU optimizations and drivers
- ✅ NVIDIA RTX 3090 drivers (Wayland-optimized)
- ✅ PipeWire audio system
- ✅ Bluetooth support
- ✅ Essential fonts
- ✅ Performance optimizations
- ✅ AUR helper (paru)

## Next Steps

Continue with:
1. **Backup Solution**: `03-backup-with-urbackup.md` - Create full system backup before desktop setup
2. **Display Manager**: `04-display-manager-greetd.md` - Set up login manager
3. **Desktop Environment**: Choose from Hyprland, QuickShell, or other desktop guides

## Test NVIDIA Settings (After Desktop Installation)
```bash
# Run this AFTER installing and starting Hyprland or another desktop environment
nvidia-settings                             # Open NVIDIA control panel
```

---

*Hardware setup guide for Arch Linux 2025 | Intel + NVIDIA + Audio | Wayland-Optimized*