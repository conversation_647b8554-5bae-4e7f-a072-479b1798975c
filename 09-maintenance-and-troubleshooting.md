# System Maintenance and Troubleshooting
## Keeping Your Arch Linux System Healthy

This guide covers system maintenance, troubleshooting, and long-term care for your Arch Linux installation.

**Prerequisites**: Complete Arch Linux installation with desktop environment

---

## 1. Btrfs Maintenance & Management

### Install Btrfs Maintenance Tools
```bash
sudo pacman -S btrfsmaintenance              # Automated Btrfs maintenance
```

### Configure Btrfs Maintenance - **OPTIONAL (Automated)**
```bash
# Enable weekly scrub (data integrity check)
sudo systemctl enable btrfsmaintenance-refresh.service
sudo <NAME_EMAIL>

# Enable monthly balance (filesystem optimization)
sudo <NAME_EMAIL>

# Enable weekly defrag for specific subvolumes
sudo <NAME_EMAIL>
```

### Manual Btrfs Commands
```bash
# Check filesystem usage
sudo btrfs filesystem usage /

# Show subvolumes
sudo btrfs subvolume list /

# Create manual snapshot
sudo btrfs subvolume snapshot / /.snapshots/manual-$(date +%Y%m%d-%H%M%S)

# Show filesystem info
sudo btrfs filesystem show

# Scrub filesystem (check for errors)
sudo btrfs scrub start /
sudo btrfs scrub status /

# Balance filesystem (optimize space usage)
sudo btrfs balance start -dusage=50 /        # Balance data chunks 50% full
```

### Snapper Management
```bash
# List snapshots
sudo snapper list

# Create manual snapshot with description
sudo snapper create --description "Before system update"

# Compare snapshot with current system
sudo snapper diff 1..0                       # Compare snapshot 1 with current

# Rollback to snapshot (from rescue environment)
# sudo snapper rollback 5

# Delete old snapshots
sudo snapper delete 5-10                     # Delete snapshots 5 through 10
```

---

## 2. System Optimization & Performance

### CPU Scaling and Performance
```bash
# Install TLP for laptop power management (optional)
sudo pacman -S tlp tlp-rdw
sudo systemctl enable tlp

# Or install auto-cpufreq for automatic CPU scaling
paru -S auto-cpufreq
sudo systemctl enable auto-cpufreq

# For desktop systems, performance governor is usually better
echo 'governor="performance"' | sudo tee /etc/default/cpupower
sudo systemctl enable cpupower
```

### Memory & I/O Optimization
```bash
# Optimize swappiness for desktop use
echo 'vm.swappiness=10' | sudo tee /etc/sysctl.d/99-swappiness.conf

# Optimize I/O scheduler for SSDs
cat > /etc/udev/rules.d/60-ioschedulers.rules << 'EOF'
# Set I/O scheduler
ACTION=="add|change", KERNEL=="sd[a-z]*|mmcblk[0-9]*", ATTR{queue/rotational}=="0", ATTR{queue/scheduler}="mq-deadline"
ACTION=="add|change", KERNEL=="sd[a-z]*", ATTR{queue/rotational}=="1", ATTR{queue/scheduler}="bfq"
ACTION=="add|change", KERNEL=="nvme[0-9]*", ATTR{queue/scheduler}="none"
EOF
```

### NVIDIA Optimization
```bash
# Enable NVIDIA Dynamic Boost (if supported)
echo 'options nvidia "NVreg_DynamicPowerManagement=0x02"' | sudo tee /etc/modprobe.d/nvidia-power.conf

# Optimize NVIDIA for gaming
echo '__GL_SHADER_DISK_CACHE=1' | sudo tee -a /etc/environment
echo '__GL_SHADER_DISK_CACHE_PATH=/tmp' | sudo tee -a /etc/environment

# Note: For Wayland/Hyprland, overclocking is configured via kernel parameters, not nvidia-xconfig
```

---

## 3. Backup Strategy & Data Protection

### Automated Btrfs Snapshots
Snapper is already configured for automatic snapshots. Verify it's working:
```bash
sudo systemctl status snapper-timeline.timer  # Should be active
sudo snapper list                             # Should show automatic snapshots
```

### External Backup with rsync
```bash
# Install backup tools
sudo pacman -S rsync rclone                  # File sync tools

# Create backup script
cat > ~/backup-system.sh << 'EOF'
#!/bin/bash
# Incremental backup to external drive

BACKUP_DEST="/mnt/backup"  # Change to your backup location
DATE=$(date +%Y%m%d-%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DEST/arch-backup-$DATE"

# Backup home directory
rsync -aAXv --delete --exclude="/home/<USER>/.cache/*" --exclude="/home/<USER>/.local/share/Trash/*" --exclude="/home/<USER>/.mozilla/*/storage/*" /home/<USER>"$BACKUP_DEST/arch-backup-$DATE/home/"

# Backup system configuration
rsync -aAXv \
  --exclude="/boot/*" \
  --exclude="/dev/*" \
  --exclude="/proc/*" \
  --exclude="/sys/*" \
  --exclude="/tmp/*" \
  --exclude="/run/*" \
  --exclude="/mnt/*" \
  --exclude="/media/*" \
  --exclude="/swap/*" \
  --exclude="/var/cache/*" \
  --exclude="/var/tmp/*" \
  / "$BACKUP_DEST/arch-backup-$DATE/system/"

echo "Backup completed: $BACKUP_DEST/arch-backup-$DATE"
EOF

chmod +x ~/backup-system.sh
```

### Cloud Backup with rclone (Optional)
```bash
# Configure cloud storage (Google Drive, Dropbox, etc.)
rclone config                                 # Follow interactive setup

# Sync important files to cloud
rclone sync ~/Documents remote:Documents
rclone sync ~/Pictures remote:Pictures
```

---

## 4. Security Hardening - **OPTIONAL**

### Firewall Configuration
```bash
sudo pacman -S ufw                           # Uncomplicated Firewall
sudo ufw enable                              # Enable firewall
sudo ufw default deny incoming              # Block incoming by default
sudo ufw default allow outgoing             # Allow outgoing
sudo ufw logging on                         # Enable logging

# Allow SSH (if needed)
sudo ufw allow ssh                          # Allow SSH connections

# Common application firewall rules
sudo ufw allow 1714:1764/udp               # KDE Connect
sudo ufw allow 1714:1764/tcp               # KDE Connect
sudo ufw allow 5353/udp                    # mDNS/Bonjour
sudo ufw allow 631/tcp                     # CUPS printing
sudo ufw allow 631/udp                     # CUPS printing
sudo ufw allow samba                       # Samba file sharing (if needed)

# Gaming-specific ports (examples)
# sudo ufw allow 27015/tcp                 # Steam
# sudo ufw allow 27015/udp                 # Steam
# sudo ufw allow 3478:3480/tcp             # Steam voice chat
# sudo ufw allow 3478:3480/udp             # Steam voice chat

# Check firewall status
sudo ufw status verbose
```

### Secure Boot (Advanced - Optional)
```bash
# Install tools for Secure Boot
sudo pacman -S sbctl

# Check Secure Boot status
sudo sbctl status

# If you want to enable Secure Boot:
# 1. Clear existing keys in UEFI
# 2. Create custom keys
# sudo sbctl create-keys
# sudo sbctl enroll-keys
# 3. Sign bootloader and kernels
# sudo sbctl sign -s /boot/EFI/BOOT/BOOTX64.EFI
# sudo sbctl sign -s /boot/vmlinuz-linux
```

### Enhanced Security
```bash
# Install security tools
sudo pacman -S clamav rkhunter lynis

# Configure ClamAV
sudo freshclam                              # Update virus definitions
sudo systemctl enable clamav-freshclam     # Auto-update definitions

# Run security audit
sudo lynis audit system                     # Comprehensive security audit
```

---

## 5. System Monitoring & Maintenance

### Install System Monitoring Tools
```bash
sudo pacman -S btop \                        # Modern resource monitor
               iotop \                       # I/O monitoring  
               nethogs \                     # Network usage per process
               nvtop \                       # NVIDIA GPU monitoring
               smartmontools \               # Hard drive health
               lm_sensors                    # Hardware sensors
```

### Weekly Maintenance Script
```bash
cat > ~/weekly-maintenance.sh << 'EOF'
#!/bin/bash
echo "=== Weekly Arch Linux Maintenance ==="

# Update system
echo "Updating system packages..."
sudo pacman -Syu

# Update AUR packages (using paru)
echo "Updating AUR packages..."
paru -Sua

# Clean package cache
echo "Cleaning package cache..."
sudo pacman -Sc --noconfirm

# Remove orphaned packages
echo "Removing orphaned packages..."
sudo pacman -Qtdq | sudo pacman -Rns - 2>/dev/null || echo "No orphans found"

# Update locate database
echo "Updating file database..."
sudo updatedb

# Check for errors
echo "Checking for systemd errors..."
systemctl --failed

# Disk usage
echo "Disk usage:"
df -h

# Snapshot cleanup
echo "Cleaning old snapshots..."
sudo snapper cleanup number

# System health
echo "System health check complete!"
EOF

chmod +x ~/weekly-maintenance.sh
```

### Create Maintenance Timer (Optional)
```bash
# Create systemd user service
mkdir -p ~/.config/systemd/user

cat > ~/.config/systemd/user/weekly-maintenance.service << 'EOF'
[Unit]
Description=Weekly System Maintenance

[Service]
Type=oneshot
ExecStart=%h/weekly-maintenance.sh
EOF

cat > ~/.config/systemd/user/weekly-maintenance.timer << 'EOF'
[Unit]
Description=Run weekly maintenance

[Timer]
OnCalendar=weekly
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Enable the timer
systemctl --user enable weekly-maintenance.timer
systemctl --user start weekly-maintenance.timer
```

---

## 6. Troubleshooting Common Issues

### Btrfs-Specific Issues

**Filesystem full but df shows space available**:
```bash
sudo btrfs balance start -dusage=50 /        # Balance data chunks
sudo btrfs balance start -musage=50 /        # Balance metadata chunks
```

**Slow performance after heavy writes**:
```bash
sudo btrfs defragment -r -v /home            # Defragment home directory
```

**Snapshot taking too much space**:
```bash
sudo snapper cleanup number                  # Clean up old snapshots
sudo btrfs quota enable /                    # Enable quotas to track usage
```

### NVIDIA Issues

**Driver not loading**:
```bash
sudo dmesg | grep -i nvidia                  # Check kernel messages
lsmod | grep nvidia                         # Check if modules loaded
sudo mkinitcpio -P                          # Rebuild initramfs
```

**Poor performance**:
```bash
nvidia-smi                                   # Check GPU utilization
__GL_SYNC_TO_VBLANK=0 your_application      # Disable vsync for testing
```

### Hyprland Issues

**Hyprland won't start**:
```bash
# Check logs
journalctl --user -u hyprland

# Verify configuration
hyprland --config ~/.config/hypr/hyprland.conf --check

# Check Wayland support
echo $XDG_SESSION_TYPE
```

**Poor performance**:
```bash
# Check GPU usage
nvidia-smi

# Monitor system resources
htop

# Check Hyprland performance
hyprctl monitors
```

### Boot Issues

**System won't boot after update**:
```bash
# Boot from Arch ISO
# Mount Btrfs root
mount -o subvol=@ /dev/nvme0n1p2 /mnt

# Chroot into system
arch-chroot /mnt

# Check recent snapshots
snapper list

# Rollback if needed
snapper rollback <snapshot_number>
```

**GRUB issues**:
```bash
sudo grub-mkconfig -o /boot/grub/grub.cfg    # Regenerate GRUB config
sudo systemctl enable grub-btrfs.path       # Enable automatic GRUB updates
```

---

## 7. Performance Monitoring and Optimization - **OPTIONAL**

### System Performance Monitoring
```bash
# Create performance monitoring script
cat > ~/system-performance.sh << 'EOF'
#!/bin/bash
echo "=== System Performance Report ==="
echo "Date: $(date)"
echo ""

echo "=== CPU Information ==="
lscpu | grep -E "(Model name|CPU\(s\)|Thread|Core)"

echo ""
echo "=== Memory Usage ==="
free -h

echo ""
echo "=== Disk Usage ==="
df -h

echo ""
echo "=== GPU Information ==="
nvidia-smi --query-gpu=name,temperature.gpu,utilization.gpu,memory.used,memory.total --format=csv,noheader,nounits 2>/dev/null || echo "NVIDIA GPU not found"

echo ""
echo "=== Top Processes by CPU ==="
ps aux --sort=-%cpu | head -10

echo ""
echo "=== Top Processes by Memory ==="
ps aux --sort=-%mem | head -10

echo ""
echo "=== System Load ==="
uptime

echo ""
echo "=== Network Connections ==="
ss -tuln | head -10
EOF

chmod +x ~/system-performance.sh
```

### Disk Health Monitoring
```bash
# Create disk health check script
cat > ~/disk-health.sh << 'EOF'
#!/bin/bash
echo "=== Disk Health Report ==="
echo "Date: $(date)"
echo ""

echo "=== SMART Status ==="
for disk in $(lsblk -d -o NAME | grep -E "(sd|nvme)" | sed 's/├─//g' | sed 's/└─//g'); do
    echo "Checking /dev/$disk:"
    sudo smartctl -H /dev/$disk 2>/dev/null || echo "SMART not supported or disk not found"
    echo ""
done

echo "=== Btrfs Filesystem Status ==="
sudo btrfs filesystem show
echo ""

echo "=== Btrfs Device Stats ==="
sudo btrfs device stats /
echo ""

echo "=== Recent Btrfs Scrub Results ==="
sudo btrfs scrub status /
EOF

chmod +x ~/disk-health.sh
```

---

## 8. System Recovery and Rescue

### Create Recovery USB
```bash
# Download latest Arch ISO
# Create bootable USB with recovery tools

# Add custom recovery scripts to USB
mkdir -p /mnt/usb/recovery-scripts
cp ~/weekly-maintenance.sh /mnt/usb/recovery-scripts/
cp ~/system-performance.sh /mnt/usb/recovery-scripts/
cp ~/disk-health.sh /mnt/usb/recovery-scripts/
```

### Emergency Recovery Commands
```bash
# Boot from Arch ISO and run these commands

# Mount Btrfs filesystem
mount -o subvol=@ /dev/nvme0n1p2 /mnt
mount -o subvol=@home /dev/nvme0n1p2 /mnt/home
mount /dev/nvme0n1p1 /mnt/boot/EFI

# Chroot into system
arch-chroot /mnt

# Fix common issues
mkinitcpio -P                               # Rebuild initramfs
grub-mkconfig -o /boot/grub/grub.cfg        # Rebuild GRUB config
systemctl enable NetworkManager             # Ensure network works

# Check and repair filesystem
btrfs check /dev/nvme0n1p2                  # Check Btrfs filesystem
```

---

## 9. Migration and Upgrades

### NVMe Migration (Future-Proofing)
```bash
# When ready to migrate to faster NVMe drive

# Method 1: Btrfs send/receive (recommended)
# Partition new NVMe drive identically
# Create same Btrfs subvolumes on new drive

# Mount both drives
mount /dev/sda2 /mnt/old
mount /dev/nvme0n1p2 /mnt/new

# Send entire filesystem to new drive
btrfs send /mnt/old | btrfs receive /mnt/new

# Copy EFI partition
dd if=/dev/sda1 of=/dev/nvme0n1p1 bs=1M

# Update fstab and reinstall GRUB on new drive
arch-chroot /mnt/new
genfstab -U /mnt/new > /etc/fstab
grub-install --target=x86_64-efi --bootloader-id=grub --efi-directory=/boot/EFI
grub-mkconfig -o /boot/grub/grub.cfg
```

### System Updates and Kernel Management
```bash
# Regular system updates
sudo pacman -Syu                            # Update all packages

# Kernel management
sudo pacman -S linux-lts                    # Install LTS kernel as backup
sudo grub-mkconfig -o /boot/grub/grub.cfg   # Update GRUB to include new kernel

# Check available kernels
pacman -Q linux                             # Current kernel
pacman -Ss linux | grep "^core/"            # Available kernels
```

---

## 10. Automation and Monitoring

### Automated Health Checks
```bash
# Create comprehensive health check script
cat > ~/system-health-check.sh << 'EOF'
#!/bin/bash
LOGFILE="/var/log/system-health.log"

echo "$(date): Starting system health check" >> "$LOGFILE"

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 90 ]; then
    echo "$(date): WARNING - Disk usage is ${DISK_USAGE}%" >> "$LOGFILE"
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEM_USAGE" -gt 90 ]; then
    echo "$(date): WARNING - Memory usage is ${MEM_USAGE}%" >> "$LOGFILE"
fi

# Check system load
LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
if (( $(echo "$LOAD > 4.0" | bc -l) )); then
    echo "$(date): WARNING - High system load: $LOAD" >> "$LOGFILE"
fi

# Check failed services
FAILED_SERVICES=$(systemctl --failed --no-legend | wc -l)
if [ "$FAILED_SERVICES" -gt 0 ]; then
    echo "$(date): WARNING - $FAILED_SERVICES failed services" >> "$LOGFILE"
    systemctl --failed --no-legend >> "$LOGFILE"
fi

# Check NVIDIA GPU temperature (if available)
if command -v nvidia-smi &> /dev/null; then
    GPU_TEMP=$(nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits)
    if [ "$GPU_TEMP" -gt 85 ]; then
        echo "$(date): WARNING - GPU temperature is ${GPU_TEMP}°C" >> "$LOGFILE"
    fi
fi

echo "$(date): System health check completed" >> "$LOGFILE"
EOF

chmod +x ~/system-health-check.sh

# Create systemd service for health checks
sudo tee /etc/systemd/system/system-health-check.service << 'EOF'
[Unit]
Description=System Health Check

[Service]
Type=oneshot
ExecStart=/home/<USER>/system-health-check.sh
User=root
EOF

sudo tee /etc/systemd/system/system-health-check.timer << 'EOF'
[Unit]
Description=Run system health check every hour

[Timer]
OnCalendar=hourly
Persistent=true

[Install]
WantedBy=timers.target
EOF

sudo systemctl enable system-health-check.timer
sudo systemctl start system-health-check.timer
```

---

## ✅ Maintenance and Troubleshooting Setup Complete!

Your system now has comprehensive maintenance and monitoring:
- ✅ Automated Btrfs maintenance
- ✅ System performance optimization
- ✅ Backup strategies
- ✅ Security hardening
- ✅ Health monitoring
- ✅ Troubleshooting procedures
- ✅ Recovery planning

## Regular Maintenance Schedule

### Daily (Automated)
- System health checks
- Log rotation
- Temporary file cleanup

### Weekly (Manual/Automated)
- System updates (`~/weekly-maintenance.sh`)
- Package cache cleanup
- Snapshot cleanup

### Monthly (Manual)
- Full system backup
- Disk health check (`~/disk-health.sh`)
- Security audit
- Performance review (`~/system-performance.sh`)

### Quarterly (Manual)
- Review and update backup strategy
- Security configuration review
- Hardware health assessment
- System optimization review

## Emergency Contacts and Resources

- **Arch Linux Wiki**: https://wiki.archlinux.org/
- **Arch Linux Forums**: https://bbs.archlinux.org/
- **Hyprland Documentation**: https://wiki.hyprland.org/
- **Btrfs Documentation**: https://btrfs.wiki.kernel.org/

---

*Maintenance and troubleshooting guide for Arch Linux 2025 | Complete System Care*