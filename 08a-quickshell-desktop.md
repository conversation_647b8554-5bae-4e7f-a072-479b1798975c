# QuickShell Desktop Setup
## Modern Qt-based Desktop Shell for Hyprland

This guide sets up QuickShell as a modern, customizable desktop shell with panels, widgets, and system integration.

**Prerequisites**:
- Complete `01-arch-base-installation.md`
- Complete `02-post-install-hardware-setup.md`
- Complete `04-display-manager-greetd.md`
- Complete `05-wayland-essentials.md`
- Complete `06-hyprland-setup.md`
- Complete `07-desktop-essentials.md`

**IMPORTANT**: This is **Option A** - Choose ONLY this option OR 8b (not multiple)

**Important**: Set your username variable first:
```bash
# Re-use the username you created
USERNAME="$(logname 2>/dev/null || whoami)"
```

---

## 1. Why QuickShell?

**QuickShell advantages**:
- **Modern Qt6 framework**: Native performance and modern UI
- **QML-based configuration**: Powerful, declarative UI language
- **Highly customizable**: Create custom widgets and layouts
- **Wayland-native**: Built specifically for Wayland compositors
- **Active development**: Regular updates and new features
- **Modular design**: Add only the components you need
- **GPU acceleration**: Smooth animations and effects
- **Multi-monitor support**: Proper handling of multiple displays

---

## 2. Install QuickShell

### Install QuickShell
```bash
# QuickShell is available from Chaotic AUR (no compilation needed!)
sudo pacman -S quickshell-git qt6-wayland qt6-declarative qt6-svg qt6-multimedia  # Install QuickShell and dependencies
```

### Install Development Tools (Optional)
```bash
# For advanced customization and development
sudo pacman -S qt6-tools qt6-doc qml-qt6     # Qt development tools and documentation
```

---

## 3. Basic QuickShell Configuration

### Create Configuration Directory
```bash
mkdir -p ~/.config/quickshell                # Create config directory
```

### Basic Shell Configuration
```bash
cat > ~/.config/quickshell/shell.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0
import Quickshell.Wayland 2.0

ShellRoot {
    Variants {
        model: Quickshell.screens
        
        PanelWindow {
            property var modelData
            screen: modelData
            
            anchors {
                left: true
                right: true  
                top: true
            }
            
            height: 40
            color: "rgba(26, 26, 26, 0.9)"
            
            Row {
                anchors.fill: parent
                anchors.margins: 8
                spacing: 12
                
                // Workspace indicator
                WorkspaceIndicator {
                    anchors.verticalCenter: parent.verticalCenter
                }
                
                // Window title
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: Quickshell.activeWindow ? Quickshell.activeWindow.title : "Desktop"
                    color: "white"
                    font.pixelSize: 14
                    elide: Text.ElideRight
                    width: 300
                }
                
                Item { Layout.fillWidth: true } // Spacer
                
                // System tray area
                Row {
                    anchors.verticalCenter: parent.verticalCenter
                    spacing: 8
                    
                    // Volume indicator
                    VolumeWidget {
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    
                    // Network indicator
                    NetworkWidget {
                        anchors.verticalCenter: parent.verticalCenter
                    }
                    
                    // Battery indicator (if laptop)
                    BatteryWidget {
                        anchors.verticalCenter: parent.verticalCenter
                        visible: Quickshell.battery.present
                    }
                    
                    // Clock
                    ClockWidget {
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
            }
        }
    }
}
EOF
```

---

## 4. Create Widget Components

### Workspace Indicator Widget
```bash
cat > ~/.config/quickshell/WorkspaceIndicator.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0
import Quickshell.Hyprland 2.0

Row {
    spacing: 4
    
    Repeater {
        model: 10
        
        Rectangle {
            width: 24
            height: 24
            radius: 12
            
            property bool isActive: Hyprland.focusedWorkspace.id === (index + 1)
            property bool hasWindows: Hyprland.workspaces.some(ws => ws.id === (index + 1) && ws.windows > 0)
            
            color: isActive ? "#33ccff" : (hasWindows ? "#555555" : "transparent")
            border.color: "#888888"
            border.width: 1
            
            Text {
                anchors.centerIn: parent
                text: index + 1
                color: parent.isActive ? "black" : "white"
                font.pixelSize: 10
                font.bold: parent.isActive
            }
            
            MouseArea {
                anchors.fill: parent
                onClicked: Hyprland.dispatch("workspace", index + 1)
            }
        }
    }
}
EOF
```

### Clock Widget
```bash
cat > ~/.config/quickshell/ClockWidget.qml << 'EOF'
import QtQuick 2.0

Column {
    spacing: 2
    
    Text {
        text: Qt.formatDateTime(new Date(), "hh:mm")
        color: "white"
        font.pixelSize: 14
        font.bold: true
        horizontalAlignment: Text.AlignHCenter
    }
    
    Text {
        text: Qt.formatDateTime(new Date(), "ddd MMM dd")
        color: "#cccccc"
        font.pixelSize: 10
        horizontalAlignment: Text.AlignHCenter
    }
    
    Timer {
        interval: 1000
        running: true
        repeat: true
        onTriggered: parent.children[0].text = Qt.formatDateTime(new Date(), "hh:mm")
    }
    
    Timer {
        interval: 60000
        running: true
        repeat: true
        onTriggered: parent.children[1].text = Qt.formatDateTime(new Date(), "ddd MMM dd")
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: {
            // Open calendar application
            Quickshell.process.start("gnome-calendar")
        }
    }
}
EOF
```

### Volume Widget
```bash
cat > ~/.config/quickshell/VolumeWidget.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0

Row {
    spacing: 4
    
    property int volume: 50
    property bool muted: false
    
    Text {
        text: parent.muted ? "🔇" : (parent.volume > 50 ? "🔊" : (parent.volume > 0 ? "🔉" : "🔈"))
        color: "white"
        font.pixelSize: 14
        anchors.verticalCenter: parent.verticalCenter
    }
    
    Text {
        text: parent.volume + "%"
        color: parent.muted ? "#888888" : "white"
        font.pixelSize: 12
        anchors.verticalCenter: parent.verticalCenter
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: {
            // Toggle mute
            Quickshell.process.start("pamixer", ["-t"])
            updateVolume()
        }
        onWheel: {
            // Scroll to change volume
            if (wheel.angleDelta.y > 0) {
                Quickshell.process.start("pamixer", ["-i", "5"])
            } else {
                Quickshell.process.start("pamixer", ["-d", "5"])
            }
            updateVolume()
        }
    }
    
    function updateVolume() {
        // Get current volume
        var process = Quickshell.process.start("pamixer", ["--get-volume"])
        process.finished.connect(function() {
            volume = parseInt(process.stdout.trim())
        })
        
        // Get mute status
        var muteProcess = Quickshell.process.start("pamixer", ["--get-mute"])
        muteProcess.finished.connect(function() {
            muted = muteProcess.stdout.trim() === "true"
        })
    }
    
    Component.onCompleted: updateVolume()
    
    Timer {
        interval: 5000
        running: true
        repeat: true
        onTriggered: parent.updateVolume()
    }
}
EOF
```

### Network Widget
```bash
cat > ~/.config/quickshell/NetworkWidget.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0

Row {
    spacing: 4
    
    property string connectionType: "unknown"
    property string connectionName: "Not connected"
    property int signalStrength: 0
    
    Text {
        text: {
            switch(parent.connectionType) {
                case "wifi": return "📶"
                case "ethernet": return "🌐"
                case "vpn": return "🔒"
                default: return "❌"
            }
        }
        color: parent.connectionType === "unknown" ? "#ff6b6b" : "white"
        font.pixelSize: 14
        anchors.verticalCenter: parent.verticalCenter
    }
    
    Text {
        text: parent.connectionName
        color: "white"
        font.pixelSize: 12
        anchors.verticalCenter: parent.verticalCenter
        elide: Text.ElideRight
        width: 100
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: {
            // Open network manager
            Quickshell.process.start("nm-connection-editor")
        }
    }
    
    function updateNetwork() {
        // Get network status using nmcli
        var process = Quickshell.process.start("nmcli", ["-t", "-f", "TYPE,NAME,STATE", "connection", "show", "--active"])
        process.finished.connect(function() {
            var lines = process.stdout.trim().split('\n')
            if (lines.length > 0 && lines[0] !== "") {
                var parts = lines[0].split(':')
                connectionType = parts[0].toLowerCase()
                connectionName = parts[1]
            } else {
                connectionType = "unknown"
                connectionName = "Not connected"
            }
        })
    }
    
    Component.onCompleted: updateNetwork()
    
    Timer {
        interval: 10000
        running: true
        repeat: true
        onTriggered: parent.updateNetwork()
    }
}
EOF
```

### Battery Widget
```bash
cat > ~/.config/quickshell/BatteryWidget.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0

Row {
    spacing: 4
    visible: batteryPresent
    
    property bool batteryPresent: false
    property int batteryLevel: 0
    property bool isCharging: false
    
    Text {
        text: {
            if (parent.isCharging) return "🔌"
            if (parent.batteryLevel > 75) return "🔋"
            if (parent.batteryLevel > 50) return "🔋"
            if (parent.batteryLevel > 25) return "🪫"
            return "🪫"
        }
        color: parent.batteryLevel < 20 && !parent.isCharging ? "#ff6b6b" : "white"
        font.pixelSize: 14
        anchors.verticalCenter: parent.verticalCenter
    }
    
    Text {
        text: parent.batteryLevel + "%"
        color: parent.batteryLevel < 20 && !parent.isCharging ? "#ff6b6b" : "white"
        font.pixelSize: 12
        anchors.verticalCenter: parent.verticalCenter
    }
    
    function updateBattery() {
        // Check if battery exists
        var checkProcess = Quickshell.process.start("ls", ["/sys/class/power_supply/BAT*"])
        checkProcess.finished.connect(function() {
            batteryPresent = checkProcess.exitCode === 0
            
            if (batteryPresent) {
                // Get battery level
                var levelProcess = Quickshell.process.start("cat", ["/sys/class/power_supply/BAT0/capacity"])
                levelProcess.finished.connect(function() {
                    batteryLevel = parseInt(levelProcess.stdout.trim())
                })
                
                // Get charging status
                var statusProcess = Quickshell.process.start("cat", ["/sys/class/power_supply/BAT0/status"])
                statusProcess.finished.connect(function() {
                    isCharging = statusProcess.stdout.trim() === "Charging"
                })
            }
        })
    }
    
    Component.onCompleted: updateBattery()
    
    Timer {
        interval: 30000
        running: true
        repeat: true
        onTriggered: parent.updateBattery()
    }
}
EOF
```

---

## 5. Advanced QuickShell Configuration

### Multi-Monitor Setup
```bash
cat > ~/.config/quickshell/shell.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0
import Quickshell.Wayland 2.0

ShellRoot {
    Variants {
        model: Quickshell.screens
        
        PanelWindow {
            property var modelData
            screen: modelData
            
            anchors {
                left: true
                right: true  
                top: true
            }
            
            height: 40
            color: "rgba(26, 26, 26, 0.9)"
            
            // Different content for primary vs secondary monitors
            Loader {
                anchors.fill: parent
                source: modelData.primary ? "PrimaryPanel.qml" : "SecondaryPanel.qml"
            }
        }
    }
    
    // Global widgets (appear on all screens)
    Variants {
        model: Quickshell.screens
        
        // Desktop widgets
        DesktopWidget {
            screen: modelData
            anchors {
                right: true
                bottom: true
            }
            width: 200
            height: 150
            margins: 20
        }
    }
}
EOF
```

### Primary Panel Configuration
```bash
cat > ~/.config/quickshell/PrimaryPanel.qml << 'EOF'
import QtQuick 2.0

Row {
    anchors.fill: parent
    anchors.margins: 8
    spacing: 12
    
    // Application launcher
    Rectangle {
        width: 32
        height: 32
        radius: 16
        color: "#33ccff"
        anchors.verticalCenter: parent.verticalCenter
        
        Text {
            anchors.centerIn: parent
            text: "⚡"
            color: "white"
            font.pixelSize: 16
        }
        
        MouseArea {
            anchors.fill: parent
            onClicked: Quickshell.process.start("wofi", ["--show", "drun"])
        }
    }
    
    // Workspace indicator
    WorkspaceIndicator {
        anchors.verticalCenter: parent.verticalCenter
    }
    
    // Window title
    Text {
        anchors.verticalCenter: parent.verticalCenter
        text: Quickshell.activeWindow ? Quickshell.activeWindow.title : "Desktop"
        color: "white"
        font.pixelSize: 14
        elide: Text.ElideRight
        width: 300
    }
    
    Item { Layout.fillWidth: true } // Spacer
    
    // System tray
    Row {
        anchors.verticalCenter: parent.verticalCenter
        spacing: 8
        
        VolumeWidget {}
        NetworkWidget {}
        BatteryWidget {}
        ClockWidget {}
    }
}
EOF
```

### Secondary Panel Configuration
```bash
cat > ~/.config/quickshell/SecondaryPanel.qml << 'EOF'
import QtQuick 2.0

Row {
    anchors.fill: parent
    anchors.margins: 8
    spacing: 12
    
    // Simplified workspace indicator
    WorkspaceIndicator {
        anchors.verticalCenter: parent.verticalCenter
    }
    
    Item { Layout.fillWidth: true } // Spacer
    
    // Minimal system info
    ClockWidget {
        anchors.verticalCenter: parent.verticalCenter
    }
}
EOF
```

---

## 6. Desktop Widgets

### System Monitor Widget
```bash
cat > ~/.config/quickshell/DesktopWidget.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0

Rectangle {
    color: "rgba(26, 26, 26, 0.8)"
    radius: 10
    border.color: "#33ccff"
    border.width: 1
    
    Column {
        anchors.fill: parent
        anchors.margins: 12
        spacing: 8
        
        Text {
            text: "System Monitor"
            color: "white"
            font.pixelSize: 14
            font.bold: true
        }
        
        // CPU usage
        Row {
            spacing: 8
            Text {
                text: "CPU:"
                color: "#cccccc"
                font.pixelSize: 12
            }
            Text {
                id: cpuText
                text: "0%"
                color: "white"
                font.pixelSize: 12
            }
        }
        
        // Memory usage
        Row {
            spacing: 8
            Text {
                text: "RAM:"
                color: "#cccccc"
                font.pixelSize: 12
            }
            Text {
                id: ramText
                text: "0%"
                color: "white"
                font.pixelSize: 12
            }
        }
        
        // GPU usage (NVIDIA)
        Row {
            spacing: 8
            Text {
                text: "GPU:"
                color: "#cccccc"
                font.pixelSize: 12
            }
            Text {
                id: gpuText
                text: "0%"
                color: "white"
                font.pixelSize: 12
            }
        }
        
        // Temperature
        Row {
            spacing: 8
            Text {
                text: "Temp:"
                color: "#cccccc"
                font.pixelSize: 12
            }
            Text {
                id: tempText
                text: "0°C"
                color: "white"
                font.pixelSize: 12
            }
        }
    }
    
    function updateStats() {
        // CPU usage
        var cpuProcess = Quickshell.process.start("sh", ["-c", "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1"])
        cpuProcess.finished.connect(function() {
            cpuText.text = parseFloat(cpuProcess.stdout.trim()).toFixed(1) + "%"
        })
        
        // Memory usage
        var memProcess = Quickshell.process.start("sh", ["-c", "free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'"])
        memProcess.finished.connect(function() {
            ramText.text = parseFloat(memProcess.stdout.trim()).toFixed(1) + "%"
        })
        
        // GPU usage (NVIDIA)
        var gpuProcess = Quickshell.process.start("nvidia-smi", ["--query-gpu=utilization.gpu", "--format=csv,noheader,nounits"])
        gpuProcess.finished.connect(function() {
            gpuText.text = gpuProcess.stdout.trim() + "%"
        })
        
        // GPU temperature
        var tempProcess = Quickshell.process.start("nvidia-smi", ["--query-gpu=temperature.gpu", "--format=csv,noheader,nounits"])
        tempProcess.finished.connect(function() {
            tempText.text = tempProcess.stdout.trim() + "°C"
        })
    }
    
    Component.onCompleted: updateStats()
    
    Timer {
        interval: 2000
        running: true
        repeat: true
        onTriggered: parent.updateStats()
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: Quickshell.process.start("kitty", ["-e", "htop"])
    }
}
EOF
```

---

## 7. QuickShell Themes and Styling

### Dark Theme Configuration
```bash
mkdir -p ~/.config/quickshell/themes

cat > ~/.config/quickshell/themes/dark.qml << 'EOF'
pragma Singleton
import QtQuick 2.0

QtObject {
    // Colors
    property color background: "#1a1a1a"
    property color surface: "#2d2d2d"
    property color primary: "#33ccff"
    property color secondary: "#ff6b6b"
    property color accent: "#4ecdc4"
    
    property color textPrimary: "#ffffff"
    property color textSecondary: "#cccccc"
    property color textDisabled: "#888888"
    
    // Sizes
    property int panelHeight: 40
    property int borderRadius: 10
    property int borderWidth: 1
    property int spacing: 8
    property int margins: 12
    
    // Fonts
    property int fontSizeSmall: 10
    property int fontSizeNormal: 12
    property int fontSizeLarge: 14
    property int fontSizeXLarge: 16
    
    // Animations
    property int animationDuration: 200
    property string animationEasing: "OutCubic"
}
EOF
```

### Apply Theme to Components
```bash
# Update shell.qml to use theme
cat > ~/.config/quickshell/shell.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0
import Quickshell.Wayland 2.0
import "themes"

ShellRoot {
    Variants {
        model: Quickshell.screens
        
        PanelWindow {
            property var modelData
            screen: modelData
            
            anchors {
                left: true
                right: true  
                top: true
            }
            
            height: DarkTheme.panelHeight
            color: DarkTheme.background
            
            Row {
                anchors.fill: parent
                anchors.margins: DarkTheme.margins
                spacing: DarkTheme.spacing
                
                WorkspaceIndicator {}
                
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: Quickshell.activeWindow ? Quickshell.activeWindow.title : "Desktop"
                    color: DarkTheme.textPrimary
                    font.pixelSize: DarkTheme.fontSizeNormal
                    elide: Text.ElideRight
                    width: 300
                }
                
                Item { Layout.fillWidth: true }
                
                Row {
                    anchors.verticalCenter: parent.verticalCenter
                    spacing: DarkTheme.spacing
                    
                    VolumeWidget {}
                    NetworkWidget {}
                    BatteryWidget {}
                    ClockWidget {}
                }
            }
        }
    }
}
EOF
```

---

## 8. Integration with Hyprland

### Add QuickShell to Hyprland Startup
```bash
# Add QuickShell to Hyprland autostart
echo 'exec-once = quickshell' >> ~/.config/hypr/hyprland.conf
```

### Create QuickShell Control Script
```bash
cat > ~/.config/hypr/scripts/quickshell-control.sh << 'EOF'
#!/bin/bash
# QuickShell control script

case "$1" in
    start)
        echo "Starting QuickShell..."
        quickshell &
        ;;
    stop)
        echo "Stopping QuickShell..."
        pkill quickshell
        ;;
    restart)
        echo "Restarting QuickShell..."
        pkill quickshell
        sleep 1
        quickshell &
        ;;
    reload)
        echo "Reloading QuickShell configuration..."
        pkill -SIGUSR1 quickshell
        ;;
    status)
        if pgrep quickshell > /dev/null; then
            echo "QuickShell is running"
        else
            echo "QuickShell is not running"
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|reload|status}"
        exit 1
        ;;
esac
EOF

chmod +x ~/.config/hypr/scripts/quickshell-control.sh
```

### Add Hyprland Key Bindings for QuickShell
```bash
cat >> ~/.config/hypr/hyprland.conf << 'EOF'

# QuickShell controls
bind = $mainMod SHIFT, B, exec, ~/.config/hypr/scripts/quickshell-control.sh restart
bind = $mainMod CTRL, B, exec, ~/.config/hypr/scripts/quickshell-control.sh reload
EOF
```

---

## 9. Debugging and Troubleshooting

### QuickShell Debugging
```bash
# Run QuickShell with debug output
quickshell --debug

# Check QuickShell logs
journalctl --user -f | grep quickshell

# Validate QML syntax
qmlscene ~/.config/quickshell/shell.qml
```

### Common Issues and Solutions

**Issue: QuickShell won't start**
```bash
# Check Qt6 installation
pacman -Q qt6-wayland qt6-declarative

# Verify configuration syntax
qmlscene ~/.config/quickshell/shell.qml

# Check for missing dependencies
ldd $(which quickshell)
```

**Issue: Widgets not updating**
```bash
# Check if system commands are available
which pamixer
which nmcli
which nvidia-smi

# Verify process execution permissions
ls -la ~/.config/quickshell/
```

**Issue: Poor performance**
```bash
# Enable GPU acceleration
export QT_QPA_PLATFORM=wayland
export QT_WAYLAND_FORCE_DPI=96

# Check GPU usage
nvidia-smi
```

---

## 10. Advanced Customization

### Custom Widget Development
```bash
# Create custom weather widget
cat > ~/.config/quickshell/WeatherWidget.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0

Column {
    spacing: 2
    
    property string weather: "Loading..."
    property string temperature: "--°C"
    
    Text {
        text: parent.weather
        color: "white"
        font.pixelSize: 12
    }
    
    Text {
        text: parent.temperature
        color: "#cccccc"
        font.pixelSize: 10
    }
    
    function updateWeather() {
        // Replace with your weather API
        var process = Quickshell.process.start("curl", ["-s", "wttr.in/London?format=%C+%t"])
        process.finished.connect(function() {
            var result = process.stdout.trim().split(' ')
            weather = result[0] || "Unknown"
            temperature = result[1] || "--°C"
        })
    }
    
    Component.onCompleted: updateWeather()
    
    Timer {
        interval: 600000 // 10 minutes
        running: true
        repeat: true
        onTriggered: parent.updateWeather()
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: Quickshell.process.start("firefox", ["https://weather.com"])
    }
}
EOF
```

### Performance Monitoring
```bash
# Create performance monitoring script
cat > ~/.config/quickshell/scripts/monitor-performance.sh << 'EOF'
#!/bin/bash
# Monitor QuickShell performance

echo "=== QuickShell Performance Monitor ==="
echo "Date: $(date)"
echo ""

echo "=== Process Information ==="
ps aux | grep quickshell | grep -v grep

echo ""
echo "=== Memory Usage ==="
pmap $(pgrep quickshell) | tail -1

echo ""
echo "=== GPU Usage ==="
nvidia-smi --query-gpu=utilization.gpu,memory.used --format=csv,noheader

echo ""
echo "=== System Load ==="
uptime
EOF

chmod +x ~/.config/quickshell/scripts/monitor-performance.sh
```

---

## ✅ QuickShell Desktop Complete!

Your QuickShell desktop environment now includes:
- ✅ Modern Qt6-based desktop shell
- ✅ Customizable panels and widgets
- ✅ System monitoring and controls
- ✅ Multi-monitor support
- ✅ Wayland-native performance
- ✅ Hyprland integration
- ✅ Themeable interface

## Next Steps

Continue with additional customization:
1. **Alternative Setup**: `08b-waybar-desktop.md` - Traditional status bar option
2. **Additional Software**: `08-additional-software.md` - Install applications and tools

## Usage Tips

- **Super + Shift + B**: Restart QuickShell
- **Super + Ctrl + B**: Reload QuickShell configuration
- **Click widgets**: Most widgets are interactive
- **Scroll on volume**: Change volume with mouse wheel
- **Right-click panels**: Access context menus (if implemented)

## Customization Ideas

- Add custom widgets for specific applications
- Create different themes for different times of day
- Implement workspace-specific panels
- Add system notification integration
- Create custom animations and transitions

---

*QuickShell desktop guide for Arch Linux 2025 | Modern Qt6 + Wayland + Hyprland*