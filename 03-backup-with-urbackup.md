# System Backup with UrBackup - **OPTIONAL**
## Complete Arch Linux System Backup Solution

This guide sets up UrBackup for comprehensive system backups after completing your base Arch installation and hardware setup.

**⚠️ COMPLETELY OPTIONAL**: This entire guide can be skipped if you don't need automated backups. The system will work perfectly without it. You can always set up backups later.

**Prerequisites**: 
- Complete `01-arch-base-installation.md` 
- Complete `02-post-install-hardware-setup.md`
- Have a backup destination (external drive, NAS, or network storage)

---

## 1. Why UrBackup for Arch Linux?

**UrBackup advantages**:
- **File-level and image-level backups**: Backup files or entire disk images
- **Incremental backups**: Only backup changed data (saves space and time)
- **Cross-platform**: Works on Linux, Windows, macOS
- **Web interface**: Easy management through browser
- **Deduplication**: Eliminates duplicate data across backups
- **Compression**: Reduces backup size
- **Scheduling**: Automatic backups on schedule
- **Btrfs integration**: Works well with Btrfs snapshots

---

## 2. Install UrBackup Client

### Install from AUR
```bash
# Install UrBackup client using paru
paru -S urbackup-client

# Alternative: Install from Chaotic AUR if available
sudo pacman -S urbackup-client
```

### Configure UrBackup Client
```bash
# Create UrBackup configuration directory
sudo mkdir -p /etc/urbackup

# Create basic client configuration
sudo tee /etc/urbackup/urbackup_client.conf << 'EOF'
# UrBackup Client Configuration
internet_server=
internet_server_port=55415
computername=arch-desktop
# Set backup paths
backup_dirs=/home,/etc,/var,/usr/local,/opt
exclude_dirs=/home/<USER>/.cache,/home/<USER>/.local/share/Trash,/var/cache,/var/tmp,/tmp
# Image backup settings
image_letters=
# Network settings
internet_compress=true
internet_encrypt=false
# Local settings
local_speed=100
internet_speed=10
EOF
```

---

## 3. Set Up UrBackup Server (Local or Remote)

### Option A: Local UrBackup Server (Recommended for single machine)

```bash
# Install UrBackup server
paru -S urbackup-server

# Create backup storage directory
sudo mkdir -p /backup/urbackup
sudo chown urbackup:urbackup /backup/urbackup

# Configure UrBackup server
sudo tee /etc/urbackup/urbackup_srv.conf << 'EOF'
# UrBackup Server Configuration
backupfolder=/backup/urbackup
max_file_incr=100
max_file_full=10
max_image_incr=30
max_image_full=5
cleanup_window=1-7/3-4
allow_config_paths=true
allow_starting_full_file_backups=true
allow_starting_incr_file_backups=true
allow_starting_full_image_backups=true
allow_starting_incr_image_backups=true
EOF

# Enable and start UrBackup server
sudo systemctl enable urbackup-server
sudo systemctl start urbackup-server
```

### Option B: External Storage Setup

```bash
# Mount external backup drive (adjust device path)
sudo mkdir -p /mnt/backup
sudo mount /dev/sdb1 /mnt/backup  # Replace with your backup drive

# Make mount permanent
echo '/dev/sdb1 /mnt/backup ext4 defaults,noatime 0 2' | sudo tee -a /etc/fstab

# Create UrBackup directory on external drive
sudo mkdir -p /mnt/backup/urbackup
sudo chown urbackup:urbackup /mnt/backup/urbackup

# Update server config to use external storage
sudo sed -i 's|backupfolder=/backup/urbackup|backupfolder=/mnt/backup/urbackup|' /etc/urbackup/urbackup_srv.conf
```

---

## 4. Configure Client-Server Connection

### Start UrBackup Services
```bash
# Enable and start UrBackup client
sudo systemctl enable urbackup-client
sudo systemctl start urbackup-client

# Check service status
sudo systemctl status urbackup-client
sudo systemctl status urbackup-server
```

### Access Web Interface
```bash
# Open web browser and navigate to:
# http://localhost:55414

# Default login:
# Username: admin
# Password: (empty - set during first login)
```

---

## 5. Configure Backup Sets

### File Backup Configuration

**In the UrBackup web interface:**

1. **Navigate to "Settings" → "File Backups"**
2. **Configure backup paths:**
   ```
   Include paths:
   /home
   /etc
   /var/log
   /usr/local
   /opt
   /root
   ```

3. **Configure exclusions:**
   ```
   Exclude paths:
   /home/<USER>/.cache
   /home/<USER>/.local/share/Trash
   /home/<USER>/Downloads
   /var/cache
   /var/tmp
   /tmp
   /proc
   /sys
   /dev
   /run
   /mnt
   /media
   ```

### Image Backup Configuration

1. **Navigate to "Settings" → "Image Backups"**
2. **Configure disk images:**
   ```
   Backup entire disk: /dev/nvme0n1
   Or specific partitions:
   - /dev/nvme0n1p1 (EFI)
   - /dev/nvme0n1p2 (Root Btrfs)
   ```

---

## 6. Btrfs Snapshot Integration

### Create Pre-Backup Snapshot Script
```bash
sudo tee /usr/local/bin/urbackup-pre-backup.sh << 'EOF'
#!/bin/bash
# UrBackup pre-backup script for Btrfs snapshots

# Create snapshot before backup
SNAPSHOT_NAME="urbackup-$(date +%Y%m%d-%H%M%S)"
snapper -c root create --description "Pre-backup snapshot: $SNAPSHOT_NAME"

# Log the snapshot
echo "$(date): Created snapshot $SNAPSHOT_NAME for UrBackup" >> /var/log/urbackup-snapshots.log
EOF

sudo chmod +x /usr/local/bin/urbackup-pre-backup.sh
```

### Create Post-Backup Cleanup Script
```bash
sudo tee /usr/local/bin/urbackup-post-backup.sh << 'EOF'
#!/bin/bash
# UrBackup post-backup cleanup script

# Clean up old snapshots (keep last 5 backup snapshots)
snapper -c root list | grep "Pre-backup snapshot" | tail -n +6 | awk '{print $1}' | while read snapshot_id; do
    if [ ! -z "$snapshot_id" ]; then
        snapper -c root delete $snapshot_id
        echo "$(date): Deleted old backup snapshot $snapshot_id" >> /var/log/urbackup-snapshots.log
    fi
done
EOF

sudo chmod +x /usr/local/bin/urbackup-post-backup.sh
```

### Configure Scripts in UrBackup
```bash
# Add scripts to UrBackup client configuration
sudo tee -a /etc/urbackup/urbackup_client.conf << 'EOF'

# Pre/post backup scripts
pre_backup_script=/usr/local/bin/urbackup-pre-backup.sh
post_backup_script=/usr/local/bin/urbackup-post-backup.sh
EOF

# Restart client to apply changes
sudo systemctl restart urbackup-client
```

---

## 7. Backup Scheduling

### Configure Automatic Backups

**In the UrBackup web interface:**

1. **Navigate to "Settings" → "General"**
2. **Set backup intervals:**
   ```
   File backup interval: 24 hours
   Full file backup interval: 7 days
   Image backup interval: 7 days
   Full image backup interval: 30 days
   ```

3. **Configure backup window:**
   ```
   Backup window: 1-7/0-6 (daily, any time)
   Or: 1-7/22-6 (daily, night hours only)
   ```

### Manual Backup Commands
```bash
# Trigger manual file backup
sudo urbackupclientctl start-backup-file

# Trigger manual image backup
sudo urbackupclientctl start-backup-image

# Check backup status
sudo urbackupclientctl status
```

---

## 8. Backup Verification and Restoration

### Verify Backups
```bash
# Check backup logs
sudo tail -f /var/log/urbackup.log

# List available backups via web interface
# Navigate to "Backups" tab in web interface

# Verify backup integrity (run periodically)
sudo urbackupclientctl verify-backup
```

### File Restoration
```bash
# Mount backup for file restoration (via web interface)
# Navigate to "Backups" → Select backup → "Download files"

# Or use command line
sudo urbackupclientctl restore-file "/path/to/file" "/restore/destination/"
```

### System Restoration (Disaster Recovery)
```bash
# Boot from Arch ISO
# Mount your drives
mount /dev/nvme0n1p2 /mnt

# Install UrBackup client in rescue environment
pacman -S urbackup-client

# Connect to backup server and restore image
# Follow UrBackup documentation for full system restore
```

---

## 9. Backup Monitoring and Maintenance

### Create Backup Status Script
```bash
sudo tee /usr/local/bin/backup-status.sh << 'EOF'
#!/bin/bash
# Check UrBackup status and send notifications

BACKUP_STATUS=$(urbackupclientctl status)
LAST_BACKUP=$(echo "$BACKUP_STATUS" | grep "Last backup")

echo "=== UrBackup Status ==="
echo "$BACKUP_STATUS"
echo ""
echo "=== Recent Snapshots ==="
snapper -c root list | tail -5
echo ""
echo "=== Disk Usage ==="
df -h /backup/urbackup 2>/dev/null || df -h /mnt/backup/urbackup 2>/dev/null
EOF

sudo chmod +x /usr/local/bin/backup-status.sh
```

### Set Up Backup Monitoring
```bash
# Create systemd service for backup monitoring
sudo tee /etc/systemd/system/backup-monitor.service << 'EOF'
[Unit]
Description=Backup Status Monitor
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/backup-status.sh
User=root

[Install]
WantedBy=multi-user.target
EOF

# Create timer for daily backup status check
sudo tee /etc/systemd/system/backup-monitor.timer << 'EOF'
[Unit]
Description=Daily Backup Status Check
Requires=backup-monitor.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Enable the timer
sudo systemctl enable backup-monitor.timer
sudo systemctl start backup-monitor.timer
```

---

## 10. Security and Best Practices

### Secure Backup Configuration
```bash
# Set proper permissions on backup directories
sudo chmod 750 /backup/urbackup
sudo chown -R urbackup:urbackup /backup/urbackup

# Configure firewall for UrBackup (if using remote server)
sudo ufw allow 55413/tcp  # UrBackup file transfer
sudo ufw allow 55414/tcp  # UrBackup web interface
sudo ufw allow 55415/tcp  # UrBackup internet service
```

### Backup Encryption (Optional)
```bash
# Enable encryption in client config
sudo sed -i 's/internet_encrypt=false/internet_encrypt=true/' /etc/urbackup/urbackup_client.conf

# Set encryption password (store securely!)
echo "encryption_key=your_secure_password_here" | sudo tee -a /etc/urbackup/urbackup_client.conf

# Restart client
sudo systemctl restart urbackup-client
```

---

## ✅ Backup System Complete!

Your backup solution now includes:
- ✅ UrBackup client and server configured
- ✅ Automated file and image backups
- ✅ Btrfs snapshot integration
- ✅ Scheduled backup monitoring
- ✅ Disaster recovery capability
- ✅ Secure backup storage

## Next Steps

Continue with:
1. **Display Manager**: `04-display-manager-greetd.md` - Set up login manager
2. **Desktop Environment**: Choose from Hyprland, QuickShell, or other desktop guides

## Important Notes

- **Test your backups regularly** by performing test restores
- **Monitor backup storage space** to prevent backup failures
- **Keep backup encryption keys secure** and backed up separately
- **Document your backup configuration** for disaster recovery scenarios

---

*Backup solution guide for Arch Linux 2025 | UrBackup + Btrfs Integration*