# Wayland Essentials Setup
## Core Wayland Tools and Environment

This guide installs essential Wayland utilities and sets up the Wayland environment for any compositor.

**Prerequisites**: 
- Complete `01-arch-base-installation.md` 
- Complete `02-post-install-hardware-setup.md`
- Complete `04-display-manager-greetd.md`
- Optionally complete `03-backup-with-urbackup.md` (**OPTIONAL** - can be skipped)

---

## 1. Why Wayland Essentials?

**Wayland advantages**:
- **Modern protocol**: Designed for current hardware and security needs
- **Better security**: Improved isolation between applications
- **GPU acceleration**: Direct rendering and better performance
- **Multi-monitor**: Superior multi-monitor support
- **Touch/HiDPI**: Native support for modern input and displays

**Essential tools needed**:
- Screenshot and screen recording tools
- Clipboard management
- Display configuration
- Media and brightness controls

---

## 2. Install Essential Wayland Utilities

### Install Core Wayland Tools
```bash
sudo pacman -S grim slurp wl-clipboard wf-recorder wlr-randr brightnessctl playerctl pamixer  # Essential Wayland utilities
```

**Package explanations**:
- `grim`: Takes screenshots on Wayland
- `slurp`: Selects screen regions for screenshots
- `wl-clipboard`: Provides `wl-copy` and `wl-paste` for clipboard
- `wf-recorder`: Records screen on Wayland
- `wlr-randr`: Configures displays (like xrandr for X11)
- `brightnessctl`: Controls screen brightness
- `playerctl`: Controls media players
- `pamixer`: Controls audio volume

---

## 3. Configure Wayland Environment

### Set Wayland Environment Variables
```bash
# Add to ~/.bashrc or ~/.zshrc
cat >> ~/.bashrc << 'EOF'

# Wayland environment variables
export XDG_SESSION_TYPE=wayland
export XDG_CURRENT_DESKTOP=wayland
export QT_QPA_PLATFORM=wayland
export GDK_BACKEND=wayland
export MOZ_ENABLE_WAYLAND=1
export XCURSOR_SIZE=24

# NVIDIA-specific (if using NVIDIA)
export LIBVA_DRIVER_NAME=nvidia
export __GLX_VENDOR_LIBRARY_NAME=nvidia
export WLR_NO_HARDWARE_CURSORS=1
EOF

# Reload environment
source ~/.bashrc
```

---

## 4. Test Wayland Tools

### Test Screenshot Tools
```bash
# Take full screenshot
grim ~/screenshot.png

# Take area screenshot (select with mouse)
grim -g "$(slurp)" ~/area-screenshot.png

# Copy screenshot to clipboard
grim -g "$(slurp)" - | wl-copy
```

### Test Clipboard
```bash
# Copy text to clipboard
echo "Hello Wayland" | wl-copy

# Paste from clipboard
wl-paste
```

### Test Display Configuration
```bash
# List displays
wlr-randr

# Example: Set display resolution (adjust for your setup)
# wlr-randr --output DP-1 --mode 1920x1080@60
```

### Test Media Controls
```bash
# Test volume control
pamixer --get-volume
pamixer --set-volume 50

# Test brightness (if supported)
brightnessctl get
brightnessctl set 50%
```

---

## 5. Next Steps

After completing this guide, you have the essential Wayland tools configured. Now choose your compositor:

- **Hyprland**: Modern tiling compositor → Complete `06-hyprland-setup.md`
- **Other compositors**: Sway, River, etc. (not covered in this guide)

---

*Wayland essentials guide for Arch Linux 2025 | Core Wayland Tools*