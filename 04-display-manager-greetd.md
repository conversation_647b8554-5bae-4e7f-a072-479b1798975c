# Display Manager Setup - Greetd
## Modern Login Manager for Wayland Compositors

This guide sets up Greet<PERSON> as a minimal, secure display manager that works perfectly with Wayland compositors like Hyprland.

**Prerequisites**: 
- Complete `01-arch-base-installation.md` 
- Complete `02-post-install-hardware-setup.md`
- Optionally complete `03-backup-with-urbackup.md` (**OPTIONAL** - can be skipped)

---

## 1. Why Greetd?

**Greetd advantages**:
- **Wayland-native**: Designed specifically for modern Wayland compositors
- **Minimal and secure**: No unnecessary features or attack surface
- **Flexible**: Works with any session type (Wayland, X11, console)
- **Lightweight**: Uses minimal system resources
- **Modern**: Actively maintained and future-proof
- **Session management**: Properly handles user sessions and permissions

**Comparison with other display managers**:
- **GDM**: Heavy, GNOME-focused, complex
- **SDDM**: Qt-based, more resource intensive
- **LightDM**: X11-focused, less Wayland support
- **Greetd**: Purpose-built for Wayland, minimal, secure

---

## 2. Install Greetd

### Install Greetd Packages
```bash
sudo pacman -S greetd greetd-tuigreet        # Minimal display manager for Wayland
```

**Package explanations**:
- `greetd`: Core display manager daemon
- `greetd-tuigreet`: Text-based greeter interface (simple and reliable)

---

## 3. Configure Greetd

### Basic Greetd Configuration
```bash
# Configure greetd to use tuigreet
sudo tee /etc/greetd/config.toml << 'EOF'
[terminal]
# The VT to run the greeter on. Can be "next", "current" or a number
# designating the VT. Defaults to "next"
vt = 1

[default_session]
# The default session, also known as the greeter.
command = "tuigreet --time --remember --remember-user-session --asterisks"
user = "greeter"

# Example of how to start a specific session (uncomment if needed)
# [initial_session]
# command = "Hyprland"
# user = "$USERNAME"
EOF
```

**Configuration options explained**:
- `vt = 1`: Use virtual terminal 1 for the greeter
- `--time`: Show current time in greeter
- `--remember`: Remember last logged-in user
- `--remember-user-session`: Remember user's last session choice
- `--asterisks`: Show asterisks when typing password

### Advanced Greetd Configuration (Optional)
```bash
# Create advanced configuration with multiple greeters
sudo tee /etc/greetd/config.toml << 'EOF'
[terminal]
vt = 1

[default_session]
command = "tuigreet --time --remember --remember-user-session --asterisks --greeting 'Welcome to Arch Linux'"
user = "greeter"

# Uncomment for automatic login (security risk!)
# [initial_session]
# command = "Hyprland"
# user = "$USERNAME"
EOF
```

---

## 4. Configure Session Detection

### Create Session Directory
```bash
# Create directory for session files
sudo mkdir -p /usr/share/wayland-sessions
sudo mkdir -p /usr/share/xsessions
```

### Create Hyprland Session File (for future use)
```bash
# Create Hyprland session file for greetd detection
sudo tee /usr/share/wayland-sessions/hyprland.desktop << 'EOF'
[Desktop Entry]
Name=Hyprland
Comment=An intelligent dynamic tiling Wayland compositor
Exec=Hyprland
Type=Application
EOF
```

### Create Console Session File
```bash
# Create console session option
sudo tee /usr/share/wayland-sessions/console.desktop << 'EOF'
[Desktop Entry]
Name=Console
Comment=Console session
Exec=/bin/bash
Type=Application
EOF
```

---

## 5. Configure User Permissions

### Add User to Required Groups
```bash
# Add user to seat management group (if not already done)
sudo usermod -aG seat $USERNAME

# Verify group membership
groups $USERNAME
```

### Configure Seat Management
```bash
# Install seat management (should already be installed)
sudo pacman -S seatd

# Enable seatd service
sudo systemctl enable seatd
sudo systemctl start seatd

# Add user to seat group
sudo usermod -aG seat $USERNAME
```

---

## 6. Enable and Test Greetd

### Enable Greetd Service
```bash
# Disable any existing display managers first
sudo systemctl disable gdm 2>/dev/null || true
sudo systemctl disable sddm 2>/dev/null || true
sudo systemctl disable lightdm 2>/dev/null || true

# Enable greetd
sudo systemctl enable greetd

# Check service status
sudo systemctl status greetd
```

### Test Greetd Configuration
```bash
# Test greetd configuration without rebooting
sudo greetd --config /etc/greetd/config.toml --check

# If no errors, the configuration is valid
```

---

## 7. Customize Greetd Appearance

### Enhanced TUIgreet Configuration
```bash
# Create enhanced greetd config with better appearance
sudo tee /etc/greetd/config.toml << 'EOF'
[terminal]
vt = 1

[default_session]
command = "tuigreet --time --remember --remember-user-session --asterisks --greeting 'Welcome to Arch Linux' --sessions /usr/share/wayland-sessions:/usr/share/xsessions --theme border=magenta;text=cyan;prompt=green;time=blue;action=white;button=yellow;container=black;input=gray"
user = "greeter"
EOF
```

### Alternative: GTK-based Greeter (Optional)
```bash
# Install GTK greeter for graphical interface
paru -S greetd-gtkgreet

# Configure GTK greeter
sudo tee /etc/greetd/config.toml << 'EOF'
[terminal]
vt = 1

[default_session]
command = "gtkgreet -l -s /usr/share/wayland-sessions"
user = "greeter"
EOF
```

---

## 8. Session Management

### Configure Environment Variables
```bash
# Create environment file for Wayland sessions
sudo tee /etc/greetd/environments << 'EOF'
# Wayland environment variables
WAYLAND_DISPLAY=wayland-1
XDG_SESSION_TYPE=wayland
XDG_CURRENT_DESKTOP=Hyprland
QT_QPA_PLATFORM=wayland
GDK_BACKEND=wayland
MOZ_ENABLE_WAYLAND=1
EOF
```

### Create Session Wrapper Script
```bash
# Create session wrapper for proper environment setup
sudo tee /usr/local/bin/wayland-session << 'EOF'
#!/bin/bash
# Wayland session wrapper for greetd

# Source environment variables
source /etc/greetd/environments 2>/dev/null || true

# Set XDG directories
export XDG_SESSION_TYPE=wayland
export XDG_CURRENT_DESKTOP=${1:-Hyprland}

# Start the session
exec "$@"
EOF

sudo chmod +x /usr/local/bin/wayland-session
```

---

## 9. Troubleshooting and Debugging

### Enable Greetd Logging
```bash
# Create log directory
sudo mkdir -p /var/log/greetd

# Configure systemd service for better logging
sudo mkdir -p /etc/systemd/system/greetd.service.d
sudo tee /etc/systemd/system/greetd.service.d/logging.conf << 'EOF'
[Service]
StandardOutput=journal
StandardError=journal
EOF

# Reload systemd and restart greetd
sudo systemctl daemon-reload
sudo systemctl restart greetd
```

### View Greetd Logs
```bash
# View greetd service logs
sudo journalctl -u greetd -f

# View all greetd-related logs
sudo journalctl | grep greetd

# Check for errors
sudo journalctl -u greetd --since "1 hour ago" | grep -i error
```

### Common Issues and Solutions

**Issue: Greetd fails to start**
```bash
# Check service status
sudo systemctl status greetd

# Check configuration syntax
sudo greetd --config /etc/greetd/config.toml --check

# Verify user exists
id greeter
```

**Issue: Session fails to start**
```bash
# Check session file permissions
ls -la /usr/share/wayland-sessions/
ls -la /usr/share/xsessions/

# Verify executable exists
which Hyprland
```

**Issue: User not in correct groups**
```bash
# Add user to required groups
sudo usermod -aG seat,video,audio,input $USERNAME

# Verify group membership
groups $USERNAME
```

---

## 10. Security Configuration

### Secure Greetd Configuration
```bash
# Set proper permissions on greetd config
sudo chmod 644 /etc/greetd/config.toml
sudo chown root:root /etc/greetd/config.toml

# Secure log directory
sudo chmod 755 /var/log/greetd
sudo chown root:root /var/log/greetd
```

### Configure PAM (if needed)
```bash
# Check PAM configuration for greetd
cat /etc/pam.d/greetd

# Default PAM config should work, but verify it exists
sudo tee /etc/pam.d/greetd << 'EOF'
#%PAM-1.0
auth       required     pam_securetty.so
auth       requisite    pam_nologin.so
auth       include      system-local-login
account    include      system-local-login
session    include      system-local-login
EOF
```

---

## 11. Integration with Desktop Environments

### Prepare for Hyprland Integration
```bash
# Ensure Hyprland session file is properly configured
sudo tee /usr/share/wayland-sessions/hyprland.desktop << 'EOF'
[Desktop Entry]
Name=Hyprland
Comment=An intelligent dynamic tiling Wayland compositor
Exec=Hyprland
Type=Application
DesktopNames=Hyprland
EOF
```

### Prepare for Other Desktop Environments
```bash
# Create GNOME session file (if needed)
sudo tee /usr/share/wayland-sessions/gnome.desktop << 'EOF'
[Desktop Entry]
Name=GNOME
Comment=GNOME on Wayland
Exec=gnome-session
Type=Application
DesktopNames=GNOME
EOF

# Create KDE session file (if needed)
sudo tee /usr/share/wayland-sessions/plasma.desktop << 'EOF'
[Desktop Entry]
Name=Plasma (Wayland)
Comment=Plasma by KDE
Exec=startplasma-wayland
Type=Application
DesktopNames=KDE
EOF
```

---

## 12. Final Testing and Reboot

### Test Configuration
```bash
# Verify all configurations
sudo greetd --config /etc/greetd/config.toml --check

# Check service is enabled
sudo systemctl is-enabled greetd

# Verify no conflicting display managers
sudo systemctl list-unit-files | grep -E "(gdm|sddm|lightdm)" | grep enabled
```

### Reboot and Test
```bash
# Reboot to test greetd
sudo reboot
```

**After reboot, you should see**:
- TUIgreet login screen on VT1
- Time display
- User selection (if multiple users)
- Session selection options
- Password prompt with asterisks

---

## ✅ Greetd Setup Complete!

Your display manager now provides:
- ✅ Modern Wayland-native login manager
- ✅ Secure session management
- ✅ User and session memory
- ✅ Multiple session support
- ✅ Proper environment setup
- ✅ Clean, minimal interface

## Next Steps

Continue with desktop environment setup:
1. **Hyprland Setup**: `05-hyprland-wayland-setup.md` - Pure Wayland compositor
2. **Desktop Shell**: Choose from QuickShell, Waybar, or hybrid setups
3. **Additional Software**: Install applications and tools as needed

## Usage Tips

- **Switch sessions**: Use arrow keys in greetd to select different sessions
- **Remember last session**: Greetd will remember your last choice
- **Console access**: Select "Console" session for command-line only
- **Troubleshooting**: Use `Ctrl+Alt+F2` to access another TTY if needed

---

*Display manager guide for Arch Linux 2025 | Greetd + Wayland Integration*