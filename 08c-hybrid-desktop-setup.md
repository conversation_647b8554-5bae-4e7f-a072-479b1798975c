# Hybrid Desktop Setup
## Combining QuickShell, Waybar, and Other Tools

This guide shows how to create hybrid desktop configurations by combining different tools for maximum flexibility and customization.

**Prerequisites**: 
- Complete `01-arch-base-installation.md` 
- Complete `02-post-install-hardware-setup.md`
- Complete `04-display-manager-greetd.md`
- Complete `05-wayland-essentials.md`
- Complete `06-hyprland-setup.md`
- Complete `07-desktop-essentials.md`

**IMPORTANT**: This is **Option C** - Choose ONLY this option OR 8a OR 8b (not multiple)

---

## 1. Why Hybrid Setups?

**Hybrid advantages**:
- **Best of both worlds**: Combine strengths of different tools
- **Flexibility**: Use different tools for different purposes
- **Customization**: Create unique desktop experiences
- **Redundancy**: Fallback options if one tool fails
- **Experimentation**: Test different configurations easily

**Common hybrid combinations**:
- QuickShell panels + Waybar for specific monitors
- Waybar for system info + Custom widgets for applications
- Different configurations for work vs gaming
- Monitor-specific layouts

---

## 2. Install Hybrid Desktop Tools

### Install QuickShell and Waybar
```bash
# Install QuickShell and Qt6 dependencies
sudo pacman -S quickshell-git \              # Modern Qt6-based shell
               qt6-wayland \                 # Qt6 Wayland support
               qt6-declarative \             # QML runtime
               qt6-svg \                     # SVG support
               qt6-multimedia \              # Media support
               waybar                        # Traditional status bar
```

---

## 3. QuickShell + Waybar Hybrid

### Scenario: QuickShell Primary + Waybar Secondary

**Use case**: QuickShell on main monitor for advanced widgets, Waybar on secondary monitors for basic info.

```bash
# Create hybrid configuration directory
mkdir -p ~/.config/hybrid-desktop
```

### QuickShell Configuration (Primary Monitor)
```bash
cat > ~/.config/quickshell/shell-hybrid.qml << 'EOF'
import QtQuick 2.0
import Quickshell 2.0
import Quickshell.Wayland 2.0

ShellRoot {
    Variants {
        model: Quickshell.screens
        
        PanelWindow {
            property var modelData
            screen: modelData
            
            // Only show QuickShell on primary monitor
            visible: modelData.primary
            
            anchors {
                left: true
                right: true  
                top: true
            }
            
            height: 40
            color: "rgba(26, 26, 26, 0.9)"
            
            Row {
                anchors.fill: parent
                anchors.margins: 8
                spacing: 12
                
                WorkspaceIndicator {}
                
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: Quickshell.activeWindow ? Quickshell.activeWindow.title : "Desktop"
                    color: "white"
                    font.pixelSize: 14
                    elide: Text.ElideRight
                    width: 300
                }
                
                Item { Layout.fillWidth: true }
                
                Row {
                    anchors.verticalCenter: parent.verticalCenter
                    spacing: 8
                    
                    VolumeWidget {}
                    NetworkWidget {}
                    BatteryWidget {}
                    ClockWidget {}
                }
            }
        }
    }
}
EOF
```

### Waybar Configuration (Secondary Monitors)
```bash
cat > ~/.config/waybar/config-secondary << 'EOF'
{
    "layer": "top",
    "position": "top",
    "height": 30,
    "spacing": 4,
    
    "modules-left": ["hyprland/workspaces"],
    "modules-center": ["clock"],
    "modules-right": ["pulseaudio", "network"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "format": "{icon}",
        "format-icons": {
            "default": ""
        }
    },
    
    "clock": {
        "format": "{:%H:%M}",
        "tooltip": false
    },
    
    "pulseaudio": {
        "format": "{volume}% {icon}",
        "format-icons": ["", "", ""],
        "on-click": "pavucontrol"
    },
    
    "network": {
        "format-wifi": "{essid} ",
        "format-ethernet": " ",
        "format-disconnected": "⚠"
    }
}
EOF
```

### Hybrid Startup Script
```bash
cat > ~/.config/hybrid-desktop/start-hybrid.sh << 'EOF'
#!/bin/bash
# Hybrid desktop startup script

# Kill existing instances
pkill quickshell
pkill waybar

# Wait a moment
sleep 1

# Start QuickShell with hybrid config
quickshell --config ~/.config/quickshell/shell-hybrid.qml &

# Start Waybar on secondary monitors only
# This requires a custom waybar build or script to detect secondary monitors
# For now, we'll use a simple approach

# Get monitor information
MONITORS=$(hyprctl monitors -j | jq -r '.[] | select(.primary == false) | .name')

# Start waybar instances for each secondary monitor
for monitor in $MONITORS; do
    WAYBAR_CONFIG_FILE=~/.config/waybar/config-secondary waybar --bar secondary-$monitor &
done

echo "Hybrid desktop started"
EOF

chmod +x ~/.config/hybrid-desktop/start-hybrid.sh
```

---

## 3. Context-Aware Desktop Switching

### Gaming Mode Configuration
```bash
cat > ~/.config/hybrid-desktop/gaming-mode.sh << 'EOF'
#!/bin/bash
# Gaming mode - minimal UI

# Kill all desktop components
pkill quickshell
pkill waybar
pkill mako

# Start minimal waybar
cat > /tmp/waybar-gaming.json << 'GAMING_EOF'
{
    "layer": "top",
    "position": "top",
    "height": 24,
    "spacing": 2,
    
    "modules-left": ["hyprland/workspaces"],
    "modules-center": [],
    "modules-right": ["clock"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "format": "{icon}",
        "format-icons": {
            "default": ""
        }
    },
    
    "clock": {
        "format": "{:%H:%M}",
        "tooltip": false
    }
}
GAMING_EOF

waybar --config /tmp/waybar-gaming.json &

# Disable notifications during gaming
# mako will not be started

echo "Gaming mode activated"
EOF

chmod +x ~/.config/hybrid-desktop/gaming-mode.sh
```

### Work Mode Configuration
```bash
cat > ~/.config/hybrid-desktop/work-mode.sh << 'EOF'
#!/bin/bash
# Work mode - full productivity setup

# Kill existing instances
pkill quickshell
pkill waybar
pkill mako

# Start full QuickShell setup
quickshell --config ~/.config/quickshell/shell.qml &

# Start notifications
mako &

# Start additional work tools
nm-applet --indicator &

# Start productivity applications
# firefox &
# code &

echo "Work mode activated"
EOF

chmod +x ~/.config/hybrid-desktop/work-mode.sh
```

### Mode Switching Script
```bash
cat > ~/.config/hybrid-desktop/switch-mode.sh << 'EOF'
#!/bin/bash
# Desktop mode switcher

MODE_FILE="$HOME/.config/hybrid-desktop/current-mode"

case "$1" in
    gaming)
        ~/.config/hybrid-desktop/gaming-mode.sh
        echo "gaming" > "$MODE_FILE"
        notify-send "Desktop Mode" "Switched to Gaming Mode" 2>/dev/null || true
        ;;
    work)
        ~/.config/hybrid-desktop/work-mode.sh
        echo "work" > "$MODE_FILE"
        notify-send "Desktop Mode" "Switched to Work Mode" 2>/dev/null || true
        ;;
    hybrid)
        ~/.config/hybrid-desktop/start-hybrid.sh
        echo "hybrid" > "$MODE_FILE"
        notify-send "Desktop Mode" "Switched to Hybrid Mode" 2>/dev/null || true
        ;;
    status)
        if [ -f "$MODE_FILE" ]; then
            echo "Current mode: $(cat "$MODE_FILE")"
        else
            echo "No mode set"
        fi
        ;;
    *)
        echo "Usage: $0 {gaming|work|hybrid|status}"
        echo "Current available modes:"
        echo "  gaming - Minimal UI for gaming"
        echo "  work   - Full productivity setup"
        echo "  hybrid - QuickShell + Waybar combination"
        exit 1
        ;;
esac
EOF

chmod +x ~/.config/hybrid-desktop/switch-mode.sh
```

---

## 4. Time-Based Desktop Switching

### Automatic Day/Night Configuration
```bash
cat > ~/.config/hybrid-desktop/time-based-switch.sh << 'EOF'
#!/bin/bash
# Time-based desktop configuration

CURRENT_HOUR=$(date +%H)

# Define time periods
WORK_START=9
WORK_END=17
EVENING_START=18
NIGHT_START=22

if [ "$CURRENT_HOUR" -ge "$WORK_START" ] && [ "$CURRENT_HOUR" -lt "$WORK_END" ]; then
    # Work hours - full productivity setup
    ~/.config/hybrid-desktop/work-mode.sh
    echo "Auto-switched to work mode ($(date))"
elif [ "$CURRENT_HOUR" -ge "$EVENING_START" ] && [ "$CURRENT_HOUR" -lt "$NIGHT_START" ]; then
    # Evening - hybrid mode
    ~/.config/hybrid-desktop/start-hybrid.sh
    echo "Auto-switched to hybrid mode ($(date))"
else
    # Night/early morning - minimal mode
    ~/.config/hybrid-desktop/gaming-mode.sh
    echo "Auto-switched to minimal mode ($(date))"
fi
EOF

chmod +x ~/.config/hybrid-desktop/time-based-switch.sh
```

### Create Systemd Timer for Automatic Switching
```bash
mkdir -p ~/.config/systemd/user

cat > ~/.config/systemd/user/desktop-mode-switch.service << 'EOF'
[Unit]
Description=Automatic Desktop Mode Switching

[Service]
Type=oneshot
ExecStart=%h/.config/hybrid-desktop/time-based-switch.sh
EOF

cat > ~/.config/systemd/user/desktop-mode-switch.timer << 'EOF'
[Unit]
Description=Switch desktop mode based on time
Requires=desktop-mode-switch.service

[Timer]
OnCalendar=hourly
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Enable the timer
systemctl --user enable desktop-mode-switch.timer
systemctl --user start desktop-mode-switch.timer
```

---

## 5. Application-Specific Desktop Configurations

### Steam Gaming Integration
```bash
cat > ~/.config/hybrid-desktop/steam-integration.sh << 'EOF'
#!/bin/bash
# Steam gaming integration

# Monitor for Steam games
while true; do
    # Check if Steam game is running (excluding Steam client itself)
    STEAM_GAMES=$(pgrep -f "steam" | wc -l)
    STEAM_CLIENT=$(pgrep "steam" | wc -l)
    
    if [ "$STEAM_GAMES" -gt "$STEAM_CLIENT" ]; then
        # Game is running - switch to gaming mode
        if [ "$(cat ~/.config/hybrid-desktop/current-mode 2>/dev/null)" != "gaming" ]; then
            ~/.config/hybrid-desktop/gaming-mode.sh
            echo "Auto-switched to gaming mode for Steam"
        fi
    else
        # No game running - switch back to previous mode
        if [ "$(cat ~/.config/hybrid-desktop/current-mode 2>/dev/null)" = "gaming" ]; then
            ~/.config/hybrid-desktop/work-mode.sh
            echo "Auto-switched back from gaming mode"
        fi
    fi
    
    sleep 10
done
EOF

chmod +x ~/.config/hybrid-desktop/steam-integration.sh
```

### Development Environment Detection
```bash
cat > ~/.config/hybrid-desktop/dev-environment.sh << 'EOF'
#!/bin/bash
# Development environment detection

# Check for development applications
DEV_APPS=("code" "nvim" "vim" "emacs" "jetbrains" "docker")
DEV_RUNNING=false

for app in "${DEV_APPS[@]}"; do
    if pgrep -f "$app" > /dev/null; then
        DEV_RUNNING=true
        break
    fi
done

if [ "$DEV_RUNNING" = true ]; then
    # Development mode - enhanced productivity
    cat > /tmp/waybar-dev.json << 'DEV_EOF'
{
    "layer": "top",
    "position": "top",
    "height": 32,
    "spacing": 4,
    
    "modules-left": ["hyprland/workspaces", "hyprland/window"],
    "modules-center": ["clock"],
    "modules-right": ["cpu", "memory", "custom/git", "pulseaudio", "network", "tray"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "format": "{name}: {icon}",
        "format-icons": {
            "1": "",
            "2": "",
            "3": "",
            "default": ""
        }
    },
    
    "hyprland/window": {
        "format": "{}",
        "max-length": 50
    },
    
    "clock": {
        "format": "{:%H:%M:%S}",
        "interval": 1
    },
    
    "cpu": {
        "format": "{usage}% ",
        "on-click": "kitty -e htop"
    },
    
    "memory": {
        "format": "{}% ",
        "on-click": "kitty -e htop"
    },
    
    "custom/git": {
        "exec": "git -C ~/Projects status --porcelain | wc -l",
        "interval": 30,
        "format": " {}",
        "tooltip": false
    },
    
    "pulseaudio": {
        "format": "{volume}% {icon}",
        "format-icons": ["", "", ""],
        "on-click": "pavucontrol"
    },
    
    "network": {
        "format-wifi": "{essid} ",
        "format-ethernet": " "
    },
    
    "tray": {
        "icon-size": 18,
        "spacing": 8
    }
}
DEV_EOF

    pkill waybar
    waybar --config /tmp/waybar-dev.json &
    echo "Development environment detected - enhanced UI active"
fi
EOF

chmod +x ~/.config/hybrid-desktop/dev-environment.sh
```

---

## 6. Monitor-Specific Configurations

### Per-Monitor Desktop Components
```bash
cat > ~/.config/hybrid-desktop/monitor-specific.sh << 'EOF'
#!/bin/bash
# Monitor-specific desktop configuration

# Get monitor information
MONITORS=$(hyprctl monitors -j)
PRIMARY_MONITOR=$(echo "$MONITORS" | jq -r '.[] | select(.primary == true) | .name')
SECONDARY_MONITORS=$(echo "$MONITORS" | jq -r '.[] | select(.primary == false) | .name')

echo "Primary monitor: $PRIMARY_MONITOR"
echo "Secondary monitors: $SECONDARY_MONITORS"

# Kill existing instances
pkill quickshell
pkill waybar

# Start QuickShell on primary monitor
quickshell --config ~/.config/quickshell/shell.qml &

# Start different Waybar configs for secondary monitors
for monitor in $SECONDARY_MONITORS; do
    case "$monitor" in
        "HDMI-A-1")
            # Media/entertainment monitor
            cat > "/tmp/waybar-$monitor.json" << 'MEDIA_EOF'
{
    "layer": "top",
    "position": "top",
    "height": 28,
    "output": "HDMI-A-1",
    
    "modules-left": ["hyprland/workspaces"],
    "modules-center": ["custom/media"],
    "modules-right": ["pulseaudio", "clock"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "format": "{icon}",
        "format-icons": {
            "default": ""
        }
    },
    
    "custom/media": {
        "exec": "playerctl metadata --format '{{artist}} - {{title}}'",
        "interval": 2,
        "max-length": 50
    },
    
    "pulseaudio": {
        "format": "{volume}% {icon}",
        "format-icons": ["", "", ""]
    },
    
    "clock": {
        "format": "{:%H:%M}"
    }
}
MEDIA_EOF
            ;;
        "DP-2")
            # Monitoring/system info monitor
            cat > "/tmp/waybar-$monitor.json" << 'MONITOR_EOF'
{
    "layer": "top",
    "position": "top",
    "height": 28,
    "output": "DP-2",
    
    "modules-left": ["hyprland/workspaces"],
    "modules-center": ["cpu", "memory", "temperature"],
    "modules-right": ["clock"],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "format": "{icon}",
        "format-icons": {
            "default": ""
        }
    },
    
    "cpu": {
        "format": "{usage}% "
    },
    
    "memory": {
        "format": "{}% "
    },
    
    "temperature": {
        "format": "{temperatureC}°C "
    },
    
    "clock": {
        "format": "{:%H:%M}"
    }
}
MONITOR_EOF
            ;;
        *)
            # Default secondary monitor config
            cp ~/.config/waybar/config-secondary "/tmp/waybar-$monitor.json"
            ;;
    esac
    
    # Start waybar for this monitor
    waybar --config "/tmp/waybar-$monitor.json" &
done

echo "Monitor-specific configuration applied"
EOF

chmod +x ~/.config/hybrid-desktop/monitor-specific.sh
```

---

## 7. Integration with Hyprland

### Add Hybrid Controls to Hyprland
```bash
cat >> ~/.config/hypr/hyprland.conf << 'EOF'

# Hybrid desktop controls
bind = $mainMod SHIFT, M, exec, ~/.config/hybrid-desktop/switch-mode.sh gaming
bind = $mainMod SHIFT, N, exec, ~/.config/hybrid-desktop/switch-mode.sh work
bind = $mainMod SHIFT, B, exec, ~/.config/hybrid-desktop/switch-mode.sh hybrid

# Monitor-specific reload
bind = $mainMod CTRL, M, exec, ~/.config/hybrid-desktop/monitor-specific.sh

# Development environment toggle
bind = $mainMod ALT, D, exec, ~/.config/hybrid-desktop/dev-environment.sh
EOF
```

### Workspace-Specific Desktop Components
```bash
cat > ~/.config/hybrid-desktop/workspace-specific.sh << 'EOF'
#!/bin/bash
# Workspace-specific desktop configuration

CURRENT_WORKSPACE=$(hyprctl activewindow -j | jq -r '.workspace.id')

case "$CURRENT_WORKSPACE" in
    1|2|3)
        # Work workspaces - full UI
        ~/.config/hybrid-desktop/work-mode.sh
        ;;
    4|5)
        # Media workspaces - media-focused UI
        pkill waybar
        waybar --config ~/.config/waybar/config-media &
        ;;
    6|7|8|9)
        # Gaming workspaces - minimal UI
        ~/.config/hybrid-desktop/gaming-mode.sh
        ;;
    *)
        # Default - hybrid mode
        ~/.config/hybrid-desktop/start-hybrid.sh
        ;;
esac
EOF

chmod +x ~/.config/hybrid-desktop/workspace-specific.sh
```

---

## 8. Advanced Hybrid Features

### Dynamic Widget Loading
```bash
cat > ~/.config/hybrid-desktop/dynamic-widgets.sh << 'EOF'
#!/bin/bash
# Dynamic widget loading based on system state

BATTERY_PRESENT=$(ls /sys/class/power_supply/BAT* 2>/dev/null | wc -l)
NVIDIA_PRESENT=$(lspci | grep -i nvidia | wc -l)
NETWORK_TYPE=$(nmcli -t -f TYPE connection show --active | head -1)

# Create dynamic waybar config
cat > /tmp/waybar-dynamic.json << 'DYNAMIC_EOF'
{
    "layer": "top",
    "position": "top",
    "height": 32,
    "spacing": 4,
    
    "modules-left": ["hyprland/workspaces", "hyprland/window"],
    "modules-center": ["clock"],
    "modules-right": [
DYNAMIC_EOF

# Add modules based on hardware
MODULES=()

# Always include basic modules
MODULES+=("pulseaudio" "network")

# Add battery if present
if [ "$BATTERY_PRESENT" -gt 0 ]; then
    MODULES+=("battery")
fi

# Add GPU monitoring if NVIDIA present
if [ "$NVIDIA_PRESENT" -gt 0 ]; then
    MODULES+=("custom/gpu")
fi

# Add network-specific modules
if [ "$NETWORK_TYPE" = "wifi" ]; then
    MODULES+=("custom/wifi-quality")
fi

MODULES+=("tray")

# Build modules array
printf '%s\n' "${MODULES[@]}" | jq -R . | jq -s . >> /tmp/waybar-dynamic.json

cat >> /tmp/waybar-dynamic.json << 'DYNAMIC_EOF2'
    ],
    
    "hyprland/workspaces": {
        "disable-scroll": true,
        "format": "{name}: {icon}",
        "format-icons": {
            "default": ""
        }
    },
    
    "hyprland/window": {
        "format": "{}",
        "max-length": 50
    },
    
    "clock": {
        "format": "{:%H:%M:%S}",
        "interval": 1
    },
    
    "pulseaudio": {
        "format": "{volume}% {icon}",
        "format-icons": ["", "", ""],
        "on-click": "pavucontrol"
    },
    
    "network": {
        "format-wifi": "{essid} ({signalStrength}%) ",
        "format-ethernet": " ",
        "on-click": "nm-connection-editor"
    }
DYNAMIC_EOF2

# Add conditional module definitions
if [ "$BATTERY_PRESENT" -gt 0 ]; then
    cat >> /tmp/waybar-dynamic.json << 'BATTERY_EOF'
    ,
    "battery": {
        "format": "{capacity}% {icon}",
        "format-icons": ["", "", "", "", ""]
    }
BATTERY_EOF
fi

if [ "$NVIDIA_PRESENT" -gt 0 ]; then
    cat >> /tmp/waybar-dynamic.json << 'GPU_EOF'
    ,
    "custom/gpu": {
        "exec": "nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits",
        "interval": 5,
        "format": "GPU: {}°C"
    }
GPU_EOF
fi

cat >> /tmp/waybar-dynamic.json << 'DYNAMIC_EOF3'
    ,
    "tray": {
        "icon-size": 18,
        "spacing": 8
    }
}
DYNAMIC_EOF3

# Start waybar with dynamic config
pkill waybar
waybar --config /tmp/waybar-dynamic.json &

echo "Dynamic configuration loaded with modules: ${MODULES[*]}"
EOF

chmod +x ~/.config/hybrid-desktop/dynamic-widgets.sh
```

---

## 9. Configuration Management

### Backup and Restore Hybrid Configs
```bash
cat > ~/.config/hybrid-desktop/backup-configs.sh << 'EOF'
#!/bin/bash
# Backup hybrid desktop configurations

BACKUP_DIR="$HOME/.config/hybrid-desktop/backups/$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup all configurations
cp -r ~/.config/quickshell "$BACKUP_DIR/"
cp -r ~/.config/waybar "$BACKUP_DIR/"
cp -r ~/.config/hypr "$BACKUP_DIR/"
cp -r ~/.config/hybrid-desktop "$BACKUP_DIR/"

echo "Configurations backed up to: $BACKUP_DIR"
EOF

chmod +x ~/.config/hybrid-desktop/backup-configs.sh
```

### Profile Management
```bash
cat > ~/.config/hybrid-desktop/profile-manager.sh << 'EOF'
#!/bin/bash
# Desktop profile management

PROFILES_DIR="$HOME/.config/hybrid-desktop/profiles"
mkdir -p "$PROFILES_DIR"

case "$1" in
    save)
        PROFILE_NAME="$2"
        if [ -z "$PROFILE_NAME" ]; then
            echo "Usage: $0 save <profile_name>"
            exit 1
        fi
        
        PROFILE_DIR="$PROFILES_DIR/$PROFILE_NAME"
        mkdir -p "$PROFILE_DIR"
        
        cp -r ~/.config/quickshell "$PROFILE_DIR/"
        cp -r ~/.config/waybar "$PROFILE_DIR/"
        cp ~/.config/hypr/hyprland.conf "$PROFILE_DIR/"
        
        echo "Profile '$PROFILE_NAME' saved"
        ;;
    load)
        PROFILE_NAME="$2"
        if [ -z "$PROFILE_NAME" ]; then
            echo "Usage: $0 load <profile_name>"
            exit 1
        fi
        
        PROFILE_DIR="$PROFILES_DIR/$PROFILE_NAME"
        if [ ! -d "$PROFILE_DIR" ]; then
            echo "Profile '$PROFILE_NAME' not found"
            exit 1
        fi
        
        # Backup current config
        ~/.config/hybrid-desktop/backup-configs.sh
        
        # Load profile
        cp -r "$PROFILE_DIR/quickshell" ~/.config/
        cp -r "$PROFILE_DIR/waybar" ~/.config/
        cp "$PROFILE_DIR/hyprland.conf" ~/.config/hypr/
        
        # Restart desktop components
        ~/.config/hybrid-desktop/start-hybrid.sh
        
        echo "Profile '$PROFILE_NAME' loaded"
        ;;
    list)
        echo "Available profiles:"
        ls -1 "$PROFILES_DIR"
        ;;
    delete)
        PROFILE_NAME="$2"
        if [ -z "$PROFILE_NAME" ]; then
            echo "Usage: $0 delete <profile_name>"
            exit 1
        fi
        
        rm -rf "$PROFILES_DIR/$PROFILE_NAME"
        echo "Profile '$PROFILE_NAME' deleted"
        ;;
    *)
        echo "Usage: $0 {save|load|list|delete} [profile_name]"
        exit 1
        ;;
esac
EOF

chmod +x ~/.config/hybrid-desktop/profile-manager.sh
```

---

## ✅ Hybrid Desktop Setup Complete!

Your hybrid desktop environment now includes:
- ✅ Multiple desktop configurations
- ✅ Context-aware switching
- ✅ Time-based automation
- ✅ Monitor-specific layouts
- ✅ Application-specific modes
- ✅ Dynamic widget loading
- ✅ Profile management system

## Next Steps

Continue with:
1. **Additional Software**: `09-additional-software.md` - Install applications and tools
2. **Maintenance Guide**: `10-maintenance-and-troubleshooting.md` - System maintenance

## Usage Tips

- **Super + Shift + M**: Gaming mode
- **Super + Shift + N**: Work mode  
- **Super + Shift + B**: Hybrid mode
- **Super + Ctrl + M**: Monitor-specific reload
- **Super + Alt + D**: Development environment toggle

## Customization Ideas

- Create seasonal desktop themes
- Add weather-based configuration switching
- Implement focus-mode for productivity
- Create presentation mode for demos
- Add voice control for mode switching

---

*Hybrid desktop guide for Arch Linux 2025 | Flexible Multi-Tool Desktop Environment*